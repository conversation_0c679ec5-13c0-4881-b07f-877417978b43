// quokka.config.js
module.exports = {
  // Enable TypeScript support
  typescript: true,

  // Enable Babel to handle JSX/ESNext features
  babel: true,

  // Allow imports from node_modules
  env: {
    runner: 'node',
  },

  // Optional: include your src folder for module resolution
  // (if you're using alias like @ or want local imports to work)
  compilerOptions: {
    baseUrl: '.',
    paths: {
      '@/*': ['src/*']
    }
  },

  // Automatically include .env values if needed
  plugins: ['dotenv'],
};
