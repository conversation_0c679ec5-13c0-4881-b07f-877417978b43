{"name": "admin-daves-roy-orbison", "version": "0.0.0", "private": true, "type": "module", "packageManager": "pnpm@8.0.0", "engines": {"node": ">=22.14.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "ts:list": "node scripts/ts-conversion.js list", "ts:convert": "node scripts/ts-conversion.js convert", "ts:check": "node scripts/ts-conversion.js check", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@cloudinary/url-gen": "^1.21.0", "@cloudinary/vue": "^1.12.1", "@formkit/auto-animate": "^0.8.2", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "^11.2.0", "firebase": "^11.0.1", "lucide-vue-next": "^0.474.0", "natural": "^8.0.1", "pinia": "^2.2.4", "tinymce": "^7.6.1", "vue": "^3.5.17", "vue-router": "^4.5.0", "vue-toastification": "2.0.0-rc.5"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.13", "@storybook/addon-docs": "^9.0.13", "@storybook/addon-vitest": "^9.0.13", "@storybook/vue3-vite": "^9.0.13", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.17.0", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.1.1", "@vue/tsconfig": "^0.7.0", "cypress": "^13.15.0", "eslint": "^9.13.0", "eslint-plugin-cypress": "^4.0.0", "eslint-plugin-storybook": "^9.0.13", "eslint-plugin-vue": "^9.29.0", "prettier": "^3.3.3", "start-server-and-test": "^2.0.8", "storybook": "^9.0.13", "typescript": "~5.6.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.1.6", "vitest": "^3.2.4", "@vitest/browser": "^3.2.4", "playwright": "^1.53.1", "@vitest/coverage-v8": "^3.2.4"}}