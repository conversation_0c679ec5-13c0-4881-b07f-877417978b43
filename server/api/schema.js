import { Router } from 'express'
import { promises as fs } from 'fs'
import path from 'path'

const router = Router()

router.post('/save', async (req, res) => {
  try {
    const { collection, schema } = req.body

    if (!collection || !schema) {
      return res.status(400).json({ error: 'Missing required fields' })
    }

    // Format the schema with proper indentation
    const formattedSchema = JSON.stringify(schema, null, 2)

    // Save to the schema file
    const schemaPath = path.resolve(process.cwd(), 'src/schema', `${collection}.json`)
    await fs.writeFile(schemaPath, formattedSchema + '\n')

    res.json({ success: true })
  } catch (error) {
    console.error('Error saving schema:', error)
    res.status(500).json({ error: error.message })
  }
})

export default router
