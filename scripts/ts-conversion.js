#!/usr/bin/env node

/**
 * TypeScript Conversion Helper Script
 *
 * This script helps with the process of converting JavaScript files to TypeScript.
 * It can:
 * 1. List all JavaScript files in the project
 * 2. Rename a JavaScript file to TypeScript
 * 3. Check TypeScript errors in a file
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.resolve(__dirname, '../src');
const EXCLUDED_DIRS = ['node_modules', 'dist', '.git'];

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

/**
 * Find all JavaScript files in a directory recursively
 * @param {string} dir - Directory to search
 * @param {string[]} fileList - Accumulator for found files
 * @returns {string[]} - List of JavaScript files
 */
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !EXCLUDED_DIRS.includes(file)) {
      findJsFiles(filePath, fileList);
    } else if (stat.isFile() && file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

/**
 * Convert a JavaScript file to TypeScript
 * @param {string} filePath - Path to the JavaScript file
 */
function convertJsToTs(filePath) {
  if (!filePath.endsWith('.js')) {
    console.error(`${colors.red}Error: File must have a .js extension${colors.reset}`);
    return;
  }

  const tsFilePath = filePath.replace(/\.js$/, '.ts');

  try {
    // Read the JavaScript file
    const content = fs.readFileSync(filePath, 'utf8');

    // Write the content to the TypeScript file
    fs.writeFileSync(tsFilePath, content, 'utf8');

    console.log(`${colors.green}Created TypeScript file: ${tsFilePath}${colors.reset}`);
    console.log(`${colors.yellow}Remember to update imports in other files!${colors.reset}`);

    // Ask if the original JS file should be deleted
    console.log(`${colors.cyan}Do you want to delete the original JavaScript file? (y/n)${colors.reset}`);
    // In a real interactive script, you'd wait for user input here
    // For now, we'll keep both files

  } catch (error) {
    console.error(`${colors.red}Error converting file: ${error.message}${colors.reset}`);
  }
}

/**
 * List all JavaScript files in the project
 */
function listJsFiles() {
  const jsFiles = findJsFiles(SRC_DIR);

  console.log(`${colors.blue}Found ${jsFiles.length} JavaScript files:${colors.reset}`);

  jsFiles.forEach((file, index) => {
    const relativePath = path.relative(process.cwd(), file);
    console.log(`${index + 1}. ${relativePath}`);
  });
}

/**
 * Main function to run the script
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    console.log(`
${colors.cyan}TypeScript Conversion Helper${colors.reset}

Usage:
  node ts-conversion.js list                  - List all JavaScript files
  node ts-conversion.js convert <file.js>     - Convert a JavaScript file to TypeScript
  node ts-conversion.js check <file.ts>       - Check TypeScript errors in a file
    `);
    return;
  }

  switch (command) {
    case 'list':
      listJsFiles();
      break;
    case 'convert':
      const filePath = args[1];
      if (!filePath) {
        console.error(`${colors.red}Error: Please specify a file to convert${colors.reset}`);
        return;
      }
      convertJsToTs(filePath);
      break;
    case 'check':
      const tsFile = args[1];
      if (!tsFile) {
        console.error(`${colors.red}Error: Please specify a file to check${colors.reset}`);
        return;
      }
      try {
        execSync(`npx tsc --noEmit ${tsFile}`, { stdio: 'inherit' });
        console.log(`${colors.green}No TypeScript errors found in ${tsFile}${colors.reset}`);
      } catch (error) {
        // The error output is already shown by tsc
      }
      break;
    default:
      console.error(`${colors.red}Error: Unknown command '${command}'${colors.reset}`);
  }
}

main();
