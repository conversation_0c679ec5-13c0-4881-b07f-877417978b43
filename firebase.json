{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"target": "admin-daves-roy-orbison", "public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "functions": [{"source": "functions", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"], "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}]}