# Component Documentation

## Base Components

### BaseCard
A foundational card component that provides consistent styling and structure for card-based content.

```vue
<BaseCard variant="default" :outline="true" :shadow="true">
  <template #header>Header content</template>
  Main content
  <template #footer>Footer content</template>
</BaseCard>
```

**Props:**
- `variant` (String): Visual style variant ('default', 'primary', 'subtle')
- `outline` (Boolean): Whether to show card border
- `shadow` (Boolean): Whether to apply drop shadow

### EntityActions
A standardized action button group for entity operations.

```vue
<EntityActions
  :is-loading="isLoading"
  :can-view="true"
  :can-edit="true"
  :can-delete="true"
  @view="handleView"
  @edit="handleEdit"
  @delete="handleDelete"
>
  <template #before>
    <!-- Additional buttons before standard actions -->
  </template>
  <template #after>
    <!-- Additional buttons after standard actions -->
  </template>
</EntityActions>
```

**Props:**
- `isLoading` (Boolean): Disable buttons during loading
- `canView` (Boolean): Show view button
- `canEdit` (Boolean): Show edit button
- `canDelete` (Boolean): Show delete button

**Events:**
- `@view`: Emitted when view button is clicked
- `@edit`: Emitted when edit button is clicked
- `@delete`: Emitted when delete button is clicked

### EntityImage
Enhanced image component with loading states and error handling.

```vue
<EntityImage
  :publicId="imageId"
  :alt="imageAlt"
  :height="200"
  :width="300"
  :fit="true"
  :rounded="false"
  @load="handleLoad"
  @error="handleError"
>
  <template #placeholder>
    <!-- Custom placeholder content -->
  </template>
</EntityImage>
```

**Props:**
- `publicId` (String): Cloudinary public ID
- `alt` (String): Image alt text (required for accessibility)
- `height` (Number|String): Image height (required to prevent CLS)
- `width` (Number|String): Image width (required to prevent CLS)
- `fit` (Boolean): Use object-fit: cover
- `rounded` (Boolean): Apply border radius
- `aspectRatio` (String): Set aspect ratio (e.g., '16/9')

**Events:**
- `@load`: Emitted when image loads successfully
- `@error`: Emitted when image fails to load

**Performance Optimizations:**
- Uses Cloudinary for automatic WebP format
- Implements lazy loading for images
- Prevents Cumulative Layout Shift (CLS) with explicit dimensions
- Provides loading and error states for better UX

## Card Components

### ActCard
Display an act with its image, name, and actions.

```vue
<template>
  <Suspense>
    <ActCard
      :act="act"
      :grid="true"
    />
    <template #fallback>
      <BaseCard>
        <div class="loading-skeleton" aria-label="Loading act information..."></div>
      </BaseCard>
    </template>
  </Suspense>
</template>

<script setup>
const act = {
  id: '123',
  name: 'The Beatles',
  photoUrl: 'acts/beatles-logo',
  description: 'Legendary rock band from Liverpool'
}
</script>
```

**Props:**
- `act` (Object): Act data object
- `grid` (Boolean): Whether to display in grid layout

**Accessibility:**
- Uses semantic HTML structure
- Includes proper ARIA labels
- Maintains color contrast ratios
- Keyboard navigable actions

### ArtistCard
Display an artist with their photo, name, and instruments.

```vue
<template>
  <ArtistCard
    :artist="artist"
    :grid="true"
  />
</template>

<script setup>
const artist = {
  id: '123',
  firstName: 'John',
  lastName: 'Lennon',
  stageName: 'John Lennon',
  photoUrl: 'artists/john-lennon',
  instruments: ['Guitar', 'Piano', 'Vocals']
}
</script>
```

**Props:**
- `artist` (Object): Artist data object
- `grid` (Boolean): Whether to display in grid layout

**Performance:**
- Implements container queries for responsive design
- Uses CSS Grid for efficient layouts
- Lazy loads images via EntityImage

### BookingCard
Display event booking information with venue and timing details.

```vue
<template>
  <BookingCard
    :event="event"
  />
</template>

<script setup>
const event = {
  id: '123',
  title: 'Summer Concert',
  when: new Date('2025-07-01T20:00:00'),
  venue: {
    name: 'Royal Albert Hall'
  },
  description: 'Annual summer concert series'
}
</script>
```

**Props:**
- `event` (Object): Event data object

**Accessibility:**
- Uses semantic time elements
- Includes proper heading structure
- Maintains readable text sizes

### EventCard
Display comprehensive event information with acts, venue, and timing.

```vue
<template>
  <EventCard
    :event="event"
  />
</template>

<script setup>
const event = {
  id: '123',
  title: 'Rock Festival 2025',
  when: new Date('2025-08-15T18:00:00'),
  venue: {
    name: 'Wembley Stadium'
  },
  acts: [
    {
      id: '1',
      name: 'The Beatles',
      logoUrls: { badge: 'acts/beatles-badge' }
    }
  ],
  description: 'Annual rock music festival'
}
</script>
```

**Props:**
- `event` (Object): Event data object

**Performance:**
- Uses CSS Grid for efficient layouts
- Implements container queries for responsive design
- Lazy loads act badges

### AdminCard
Display admin user information with photo and contact details.

```vue
<template>
  <AdminCard
    :admin="admin"
  />
</template>

<script setup>
const admin = {
  id: '123',
  displayName: 'John Doe',
  email: '<EMAIL>',
  photoURL: 'admins/john-doe'
}
</script>
```

**Props:**
- `admin` (Object): Admin user data object

**Security:**
- Implements proper access control
- Sanitizes user data display
- Handles sensitive information appropriately

### DashboardCard
A flexible card component for dashboard widgets.

```vue
<template>
  <DashboardCard
    title="Statistics"
    icon="📊"
    variant="primary"
  >
    <div class="stats" role="list">
      <p role="listitem">Total Users: 1,234</p>
      <p role="listitem">Active Events: 56</p>
    </div>
    <template #footer>
      <button>View Details</button>
    </template>
  </DashboardCard>
</template>
```

**Props:**
- `title` (String): Card title
- `icon` (String): Optional icon
- `variant` (String): Card style variant

### DashboardSchemaCard
Specialized card for displaying database schema information.

```vue
<template>
  <DashboardSchemaCard
    span="1 / 3"
  />
</template>
```

**Props:**
- `span` (String): Grid column span value (e.g., '1', '1 / 3')

## Layout Patterns

### Grid Layout
```vue
<template>
  <div class="grid" role="grid">
    <ActCard
      v-for="act in acts"
      :key="act.id"
      :act="act"
      :grid="true"
      role="gridcell"
    />
  </div>
</template>

<style scoped>
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  container-type: inline-size;
}

@container (max-width: 600px) {
  .grid {
    grid-template-columns: 1fr;
  }
}
</style>
```

### List Layout
```vue
<template>
  <div class="list" role="list">
    <EventCard
      v-for="event in events"
      :key="event.id"
      :event="event"
      role="listitem"
    />
  </div>
</template>

<style scoped>
.list {
  display: grid;
  gap: 1rem;
}
</style>
```

### Dashboard Layout
```vue
<template>
  <div class="dashboard">
    <DashboardCard
      title="Recent Acts"
      icon="🎵"
    >
      <Suspense>
        <template #default>
          <ActCard
            v-for="act in recentActs"
            :key="act.id"
            :act="act"
          />
        </template>
        <template #fallback>
          <div class="loading-skeleton" aria-label="Loading recent acts..."></div>
        </template>
      </Suspense>
    </DashboardCard>

    <DashboardSchemaCard span="1 / -1" />
  </div>
</template>

<style scoped>
.dashboard {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  container-type: inline-size;
}

@container (max-width: 1200px) {
  .dashboard {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (max-width: 800px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
}

.loading-skeleton {
  height: 200px;
  background: linear-gradient(
    90deg,
    var(--color-background-mute) 25%,
    var(--color-background-soft) 50%,
    var(--color-background-mute) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
</style>
```

## Best Practices

### Performance
1. Use `Suspense` for async components
2. Implement lazy loading for images
3. Use container queries for efficient responsive design
4. Provide loading states to prevent layout shifts
5. Use CSS Grid for performant layouts
6. Implement proper chunking with dynamic imports

### Accessibility
1. Use semantic HTML elements
2. Include proper ARIA roles and labels
3. Maintain color contrast ratios
4. Ensure keyboard navigation
5. Provide proper heading structure
6. Include loading state announcements

### Responsive Design
1. Use container queries for component-level responsiveness
2. Implement mobile-first approach
3. Use CSS Grid for flexible layouts
4. Maintain readable text sizes across viewports
5. Handle touch interactions appropriately

### Error Handling
1. Provide meaningful error messages
2. Include fallback UI states
3. Handle loading states gracefully
4. Implement proper error boundaries
5. Log errors appropriately
