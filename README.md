# admin-daves-roy-orbison

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm test:unit
```

### Run End-to-End Tests with [Cypress](https://www.cypress.io/)

```sh
pnpm test:e2e:dev
```

This runs the end-to-end tests against the Vite development server.
It is much faster than the production build.

But it's still recommended to test the production build with `test:e2e` before deploying (e.g. in CI environments):

```sh
pnpm build
pnpm test:e2e
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```

## TypeScript Conversion Plan

This project is being converted from JavaScript to TypeScript. The following steps outline the conversion process:

### Conversion Status

- [x] TypeScript configuration is set up
- [x] Initial type definitions in `src/types`
- [x] Some models already converted to TypeScript
- [ ] Convert remaining models to TypeScript
- [ ] Convert composables to TypeScript
- [ ] Convert Vue components to TypeScript
- [ ] Convert utilities to TypeScript
- [ ] Convert services to TypeScript

### Conversion Guidelines

1. **File Extensions**: Change `.js` to `.ts` and `.vue` files should use `<script setup lang="ts">`.
2. **Type Definitions**:
   - Use interfaces for object shapes
   - Use type aliases for unions and complex types
   - Keep related types in the same file as their implementation when specific to one file
   - Move shared types to `src/types` directory
3. **Class Conversions**:
   - Add property declarations at the top of the class
   - Add parameter types to methods
   - Add return types to methods
   - Use access modifiers (public, private, protected) where appropriate
4. **Vue Components**:
   - Use `defineProps<{...}>()` syntax for prop definitions when possible
   - Add type annotations to all computed properties and methods
   - Use `as` type assertions sparingly and only when necessary
5. **Composables**:
   - Define input parameter interfaces
   - Define return type interfaces
   - Add proper typing to refs and reactive objects

### Running Type Checks

```bash
pnpm type-check
```
