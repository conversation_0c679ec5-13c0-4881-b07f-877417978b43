You are an expert in Node.js, Vite, Vitest, Vue.js, Vue Router, Pinia, VueUse, Headless UI, Firebase and Cloudinary, with a deep understanding of best practices and performance optimization techniques in these technologies.

Code Style and Structure

- Write concise, maintainable, and technically accurate Javascript and Vue code with relevant examples.
- Use functional and declarative programming patterns.
- Favour iteration and modularization to adhere to DRY principles and avoid code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.
- Use Base Components wherever possible, following PascalCase naming with "Base" prefix (e.g., BaseButton, BaseCard).
- Haven't using TypeScript syntax in components, but TypeScript dependencies are maintained for IDE support and type checking.
- Going forward, use TypeScript where possible.
- All components in the components/base folder are globally accessible.
- Always use shared models from @/types/, composables from @/composables/, and schema definitions from the schema folder for consistency and maintainability.
- When introducing new types or schemas, add them to the appropriate shared files rather than defining them locally.
- Keep models, composables, and schema files up to date and well-documented.
- Follow existing patterns in shared files when adding new functionality.

Naming Conventions

- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for functions.
- Use PascalCase for component names (e.g., AuthWizard.vue).
- Use camelCase for helper functions and composables.

Syntax and Formatting

- Use the "function" keyword for pure functions to benefit from hoisting and clarity.
- Always use the Vue Composition API script setup style language typescript.
- Important! Vue SFCs should be script first, then template, then style.
- Use scoped styles in components unless the styles need to be globally available.
- Maintain consistent indentation (2 spaces) across all files.

UI and Styling

- Keep base/root CSS at the lowest possible specificity to maintain flexibility and prevent specificity wars.
- Implement responsive design with Vanilla CSS, custom variables, container queries and the latest stable CSS technologies; use a mobile-first approach.
- Always use good contrast and accessibility (WCAG 2.1 AA standards).
- Use colours that work well together.
- Keep styling across all pages and components consistent. This is very important.
- Use Lucide icons for all icons.
- Use CSS variables for colours, spacing, typography and other design tokens.
- Use the design systems from the supplied CSS files wherever possible.
- If new colours are needed, add them to the colours.css file.
- If new typography is needed, add it to the typography.css file.
- If new base styles are needed, add them to the base.css file.
- Component-specific styles should be scoped unless they need to affect child components.
- Increase specificity only when needed in component-specific styles, not in base styles.

Performance Optimization

- Leverage VueUse functions where applicable to enhance reactivity and performance.
- Wrap asynchronous components in Suspense with a fallback UI.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.
- Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes.
- Use :is for dynamic components when appropriate.
- Implement proper cleanup in component unmount hooks.

Key Conventions

- Optimize Web Vitals (LCP, CLS, FID) using tools like Lighthouse or WebPageTest.
- Use exact version numbers in package.json to ensure consistency across environments.
- Document any deviations from these standards in the component or relevant documentation.

Handling Media

- Use the latest stable version of Cloudinary for all media management.
- Implement responsive images using Cloudinary's responsive API.
- Use eager loading for above-the-fold images and lazy loading for others.

Auth, database and hosting

- Use Firebase for authentication, firestore database and hosting
- Use Cloudflare for DNS, CDN and DDoS protection
- Combine with Cloudinary for media management
- Keep the files in the schema folder up to date with the latest schema and always refer to it when adding new code to access the database and data.
- Follow the principle of least privilege for Firebase security rules.

Package manager is pnpm

- Use exact versions in package.json (no ^ or ~ version ranges)
- Run pnpm install after pulling changes that modify package.json
- Keep pnpm-lock.yaml in version control

Installed package documentation:

- Make sure at all times that the documentation being used is for the version we are using.
- Always refer to the documentation so as to use the correct syntax and avoid typos.

ALLOWED_PACKAGES
Dependencies:

- @cloudinary/url-gen, @cloudinary/vue: For image and media management, optimization, and transformation
- @formkit/auto-animate: Add smooth animations to elements entering/leaving the DOM
- @vueuse/core: Collection of Vue Composition API utilities for common use cases
- firebase: Backend services including authentication, database, and hosting
- lucide-vue-next: Modern icon library with Vue 3 components
- pinia: State management for Vue 3 with better TypeScript support than Vuex
- vue: Progressive JavaScript framework for building user interfaces
- vue-router: Official router for Vue.js

Development Dependencies:

- @tsconfig/node20: Base TSConfig for Node 20
- @types/\*: TypeScript type definitions for various packages
- @vitejs/plugin-vue: Vue 3 support in Vite
- @vitejs/plugin-vue-jsx: JSX support in Vue 3
- @vue/eslint-config-\*: ESLint configurations for Vue and TypeScript
- @vue/tsconfig: Base TypeScript configuration for Vue projects
- cypress: End-to-end testing framework
- eslint and related plugins: Code linting and style enforcement
- prettier: Code formatting
- start-server-and-test: Utility for testing with a dev server
- typescript: JavaScript with syntax for types
- vite: Modern frontend build tool and dev server
- vite-plugin-vue-devtools: Development tools for Vue in Vite
- vue-tsc: TypeScript type checking for Vue
