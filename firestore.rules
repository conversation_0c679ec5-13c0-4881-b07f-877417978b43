rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {  
		match /acts/{actId} {    
      allow read: if true;
      allow write: if request.auth.token.admin;
    } 
    
    match /agents/{agentId} {    
    	allow read, write: if request.auth.token.admin;
    }
    
    match /artists/{artistId} {
    	allow read, write: if request.auth.token.admin;
    }
    
    match /artistAvailability/{artistAvailabilityId} {
    	allow read, write: if request.auth.token.admin;
    }

		match /emails/{emailId} {
    	allow create: if true;
    }
    
		match /events/{eventId} {    
      allow read: if true;
      allow write: if request.auth.token.admin;
    }
    
    match /eventNotes/{noteId} {
    	allow read, write: if request.auth.token.admin;
    }
        
    match /repertoire/{repertoireId} {    
      allow read: if true;
      allow write: if request.auth.token.admin;
    }
    
    match /repertoireNotes/{repertoireNoteId} {    
    	allow read, write: if request.auth.token.admin;
    }

    match /subscribers/{subscriberId} {
      allow create: if true;
      allow read, write: if request.auth.token.admin;
    }
                      
		match /tasks/{taskId} {    
      allow read, write: if request.auth.token.admin;
    }
    
    match /testimonials/{testimonialId} {    
      allow read: if true;
      allow write: if request.auth.token.admin;
    }
    
    match /users/{userId} {
    	allow read: if (request.auth.uid == userId) || request.auth.token.admin;
    	allow update, delete: if (request.auth.uid == userId) || request.auth.token.admin;
      allow create: if request.auth != null;
      
      match /activity/{activityId} {
        allow read: if (request.auth.uid == userId) || request.auth.token.admin;
        // Only allow server-side writes and admin writes
        allow write: if request.auth.token.admin;
        
        // Validate activity document structure
        match /{document=**} {
          allow write: if request.auth.token.admin && 
            request.resource.data.keys().hasAll(['timestamp', 'action']) &&
            request.resource.data.timestamp is timestamp &&
            request.resource.data.action is string;
        }
      }
    }
    
		match /venues/{venueId} {    
      allow read: if true;
      allow write: if request.auth.token.admin;
    }

    match /venueNotes/{venueNoteId} {
    	allow read, write: if request.auth.token.admin;
    }
    
    match /videos/{videoId} {
    	allow read: if true;
      allow write: if request.auth.token.admin;
    }
  }
}