import { ref, onMounted, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import {
  collection,
  query,
  where,
  onSnapshot,
  addDoc,
  deleteDoc,
  doc,
  Timestamp,
  serverTimestamp,
  updateDoc,
  type Unsubscribe,
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'

interface UnavailabilityDate {
  id: string
  startDate: Date
  endDate: Date
  createdAt: Date
  updatedAt: Date
  artistId: string
  reason?: string
}

export function useArtistAvailability() {
  const { db } = useFirebase()
  const unavailableDates: Ref<UnavailabilityDate[]> = ref([])
  const isLoading = ref(true)
  const error: Ref<string | null> = ref(null)
  let unsubscribe: Unsubscribe | null = null

  const subscribeToAvailability = () => {
    try {
      const q = query(
        collection(db, 'artistAvailability'),
        where('startDate', '>=', Timestamp.now()),
      )

      unsubscribe = onSnapshot(
        q,
        snapshot => {
          unavailableDates.value = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            startDate: doc.data().startDate?.toDate(),
            endDate: doc.data().endDate?.toDate(),
            createdAt: doc.data().createdAt?.toDate(),
            updatedAt: doc.data().updatedAt?.toDate(),
          })) as UnavailabilityDate[]
          isLoading.value = false
        },
        err => {
          console.error('Availability subscription error:', err)
          error.value = 'Failed to load availability'
          isLoading.value = false
        },
      )
    } catch (e) {
      console.error('Error setting up availability subscription:', e)
      error.value = 'Failed to subscribe to availability'
      isLoading.value = false
    }
  }

  const markUnavailable = async (
    artistId: string,
    startDate: Date,
    endDate: Date,
    reason = '',
  ) => {
    try {
      const docRef = await addDoc(collection(db, 'artistAvailability'), {
        artistId,
        startDate,
        endDate,
        reason,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })
      return docRef
    } catch (error) {
      console.error('Error marking unavailability:', error)
      throw error
    }
  }

  const removeUnavailability = async (id: string) => {
    try {
      if (!id) {
        throw new Error('Availability ID is required')
      }

      await deleteDoc(doc(db, 'artistAvailability', id))
      return true
    } catch (e) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error removing unavailability:', e)
      }
      return false
    }
  }

  const updateUnavailability = async (
    id: string,
    artistId: string,
    startDate: Date,
    endDate: Date,
    reason = '',
  ) => {
    try {
      await updateDoc(doc(db, 'artistAvailability', id), {
        artistId,
        startDate,
        endDate,
        reason,
        updatedAt: serverTimestamp(),
      })
      return true
    } catch (error) {
      console.error('Error updating unavailability:', error)
      throw error
    }
  }

  const markAvailable = async (artistId: string, unavailabilityId: string) => {
    try {
      if (!unavailabilityId) {
        throw new Error('Unavailability ID is required')
      }

      await deleteDoc(doc(db, 'artistAvailability', unavailabilityId))
      return true
    } catch (error) {
      console.error('Error marking artist as available:', error)
      throw error
    }
  }

  // Start subscription
  subscribeToAvailability()

  // Return cleanup function
  const cleanup = () => {
    if (unsubscribe) {
      unsubscribe()
    }
  }

  return {
    unavailableDates,
    isLoading,
    error,
    markUnavailable,
    removeUnavailability,
    updateUnavailability,
    markAvailable,
    cleanup,
  }
}
