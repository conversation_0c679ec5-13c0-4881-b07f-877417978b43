import { onUnmounted } from 'vue'

interface TimeOfDay {
  hours: number
  minutes: number
}

/**
 * Parse time string in 24-hour format (HH:mm)
 * @param timeString Time in format "HH:mm" (e.g., "00:00" for midnight, "13:30" for 1:30 PM)
 * @returns TimeOfDay object or null if invalid
 */
function parseTimeString(timeString: string): TimeOfDay | null {
  const match = timeString.match(/^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/)
  if (!match) return null

  return {
    hours: parseInt(match[1]),
    minutes: parseInt(match[2]),
  }
}

/**
 * Calculate milliseconds until next occurrence of specified time
 * @param targetTime Time to calculate until
 * @returns Milliseconds until next occurrence
 */
function getMsUntilTime(targetTime: TimeOfDay): number {
  const now = new Date()
  const next = new Date(now)

  // Set the target time
  next.setHours(targetTime.hours, targetTime.minutes, 0, 0)

  // If the time has already passed today, set for tomorrow
  if (next <= now) {
    next.setDate(next.getDate() + 1)
  }

  return next.getTime() - now.getTime()
}

/**
 * Composable that sets up a timer to trigger a callback at a specific time each day
 * @param callback Function to be called at the specified time
 * @param timeString Optional time in 24-hour format "HH:mm" (default "00:00" for midnight)
 * @returns cleanup function
 */
export function useDailyTimer(
  callback: () => void,
  timeString: string = '00:00',
) {
  let timer: number | undefined

  // Default to midnight if time string is invalid
  let targetTime: TimeOfDay = { hours: 0, minutes: 0 }
  const parsedTime = parseTimeString(timeString)

  if (parsedTime) {
    targetTime = parsedTime
  } else {
    console.error(
      `Invalid time format: ${timeString}. Using midnight (00:00) instead.`,
    )
  }

  // Function to setup the timer
  function setupTimer() {
    // Clear any existing timer
    if (timer) {
      clearTimeout(timer)
    }

    // Set timer for next occurrence
    timer = window.setTimeout(() => {
      callback()
      // Setup next timer
      setupTimer()
    }, getMsUntilTime(targetTime))
  }

  // Start the timer
  setupTimer()

  // Cleanup on unmount
  onUnmounted(() => {
    if (timer) {
      clearTimeout(timer)
    }
  })

  // Return cleanup function in case manual cleanup is needed
  return () => {
    if (timer) {
      clearTimeout(timer)
    }
  }
}
