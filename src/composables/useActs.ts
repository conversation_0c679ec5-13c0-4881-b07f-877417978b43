import { ref } from 'vue'
import {
  collection,
  doc,
  getDoc,
  onSnapshot,
  updateDoc,
  addDoc,
  Timestamp,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/useFirebase'
import { Act } from '@/models/Act'

const { db } = useFirebase()

export function useActs() {
  const acts = ref<Act[]>([])
  const currentAct = ref<Act | null>(null)
  const isLoading = ref(false)
  let unsubscribeAct: (() => void) | null = null
  let unsubscribeActs: (() => void) | null = null

  const subscribeToAct = async (actId: string) => {
    isLoading.value = true
    cleanup() // Clean up any existing subscription

    unsubscribeAct = onSnapshot(
      doc(db, 'acts', actId),
      doc => {
        if (doc.exists()) {
          const data = doc.data()
          currentAct.value = new Act({
            id: doc.id,
            ...data,
          } as Act & { id: string })
        } else {
          currentAct.value = null
        }
        isLoading.value = false
      },
      error => {
        console.error('Error fetching act:', error)
        isLoading.value = false
      },
    )
  }

  const subscribeToActs = () => {
    isLoading.value = true
    cleanup() // Clean up any existing subscription

    unsubscribeActs = onSnapshot(
      collection(db, 'acts'),
      snapshot => {
        acts.value = snapshot.docs.map(
          doc =>
            new Act({
              id: doc.id,
              ...doc.data(),
            } as Act & { id: string }),
        )
        isLoading.value = false
      },
      error => {
        console.error('Error fetching acts:', error)
        isLoading.value = false
      },
    )
  }

  const cleanup = () => {
    if (unsubscribeAct) {
      unsubscribeAct()
      unsubscribeAct = null
    }
    if (unsubscribeActs) {
      unsubscribeActs()
      unsubscribeActs = null
    }
    currentAct.value = null
  }

  const updateAct = async (id: string, data: Partial<Act>) => {
    const docRef = doc(db, 'acts', id)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now(),
    })
  }

  const createAct = async (data: Partial<Act>) => {
    const docRef = await addDoc(collection(db, 'acts'), {
      ...data,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    })
    return docRef.id
  }

  const fetchAct = async (id: string): Promise<Act | null> => {
    const docRef = doc(db, 'acts', id)
    const docSnap = await getDoc(docRef)
    if (docSnap.exists()) {
      return new Act({
        id: docSnap.id,
        ...docSnap.data(),
      } as Act & { id: string })
    }
    return null
  }

  return {
    acts,
    currentAct,
    isLoading,
    createAct,
    fetchAct,
    updateAct,
    subscribeToAct,
    subscribeToActs,
    cleanup,
  }
}
