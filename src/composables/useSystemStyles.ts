import { computed, unref } from 'vue'

// const TEXT_ON_LIGHT = 'var(--black)'
const TEXT_ON_DARK = 'var(--white)'

export type StyleVariant = 'solid' | 'outline' | 'ghost' | 'dark'
export type StylePurpose =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'accent'
  | 'success'
  | 'danger'
  | 'warning'
  | 'info'

const baseColors: Record<StylePurpose, string> = {
  default: 'var(--color-brand)',
  primary: 'var(--color-primary)',
  secondary: 'var(--color-secondary)',
  accent: 'var(--color-accent)',
  success: 'var(--color-success)',
  danger: 'var(--color-danger)',
  warning: 'var(--color-warning)',
  info: 'var(--color-info)',
}

const createVariantStyles = (color: string) => ({
  solid: {
    background: color,
    border: 'transparent',
    text: TEXT_ON_DARK,
    hover: { background: `color-mix(in srgb, ${color}, white 20%)` },
    checked: { background: `color-mix(in srgb, ${color}, black 20%)` },
    disabled: { opacity: '0.5', cursor: 'not-allowed' },
    focused: { outline: `2px solid ${color}`, outlineOffset: '2px' },
    pressed: { background: `color-mix(in srgb, ${color}, black 20%)` },
  },
  outline: {
    background: 'transparent',
    border: color,
    text: color,
    hover: { border: `color-mix(in srgb, ${color}, white 20%)` },
    checked: { background: color, text: TEXT_ON_DARK },
    disabled: { opacity: '0.5', cursor: 'not-allowed' },
    focused: { outline: `2px solid ${color}`, outlineOffset: '2px' },
    pressed: { background: `color-mix(in srgb, ${color}, transparent 90%)` },
  },
  ghost: {
    background: `color-mix(in srgb, ${color}, transparent 75%)`,
    border: 'transparent',
    text: color,
    hover: { background: `color-mix(in srgb, ${color}, transparent 20%)` },
    checked: { background: `color-mix(in srgb, ${color}, transparent 50%)` },
    disabled: { opacity: '0.5', cursor: 'not-allowed' },
    focused: { outline: `2px solid ${color}`, outlineOffset: '2px' },
    pressed: { background: `color-mix(in srgb, ${color}, transparent 60%)` },
  },
  dark: {
    background: 'var(--color-surface-black)',
    border: 'transparent',
    text: color,
    hover: { text: `color-mix(in srgb, ${color}, white 20%)` },
    checked: { background: `color-mix(in srgb, ${color}, black 80%)` },
    disabled: { opacity: '0.5', cursor: 'not-allowed' },
    focused: { outline: `2px solid ${color}`, outlineOffset: '2px' },
    pressed: { text: `color-mix(in srgb, ${color}, black 20%)` },
  },
})

const createToggleVariants = (color: string) => ({
  solid: {
    background: `color-mix(in srgb, ${color}, transparent 75%)`,
    border: 'transparent',
    thumb: color,
    checked: {
      background: color,
      thumb: `color-mix(in srgb, ${color}, white 40%)`,
    },
  },
  outline: {
    background: 'transparent',
    border: color,
    thumb: color,
    checked: { thumb: `color-mix(in srgb, ${color}, white 40%)` },
  },
  ghost: {
    background: `color-mix(in srgb, ${color}, transparent 90%)`,
    border: 'transparent',
    thumb: color,
    checked: {
      background: `color-mix(in srgb, ${color}, transparent 75%)`,
      thumb: `color-mix(in srgb, ${color}, white 40%)`,
    },
  },
  dark: {
    background: 'var(--color-surface-black)',
    border: `color-mix(in srgb, ${color}, black 40%)`,
    thumb: `color-mix(in srgb, ${color}, black 20%)`,
    checked: {
      border: color,
      thumb: color,
    },
  },
})

export function useSystemStyles(
  variant: StyleVariant = 'solid',
  purpose: StylePurpose = 'default',
  state = {
    isHovered: false,
    isChecked: false,
    isDisabled: false,
    isFocused: false,
    isPressed: false,
  },
) {
  return {
    currentStyles: computed(() => {
      const baseStyle = createVariantStyles(baseColors[unref(purpose)])[
        unref(variant)
      ]

      return {
        ...baseStyle,
        ...(state.isHovered && baseStyle.hover ? baseStyle.hover : {}),
        ...(state.isChecked && baseStyle.checked ? baseStyle.checked : {}),
        ...(state.isDisabled && baseStyle.disabled ? baseStyle.disabled : {}),
        ...(state.isFocused && baseStyle.focused ? baseStyle.focused : {}),
        ...(state.isPressed && baseStyle.pressed ? baseStyle.pressed : {}),
      }
    }),
  }
}

export function useToggleStyles(
  variant: StyleVariant,
  purpose: StylePurpose,
  isChecked: boolean,
) {
  return {
    currentStyles: computed(() => {
      const styles = createToggleVariants(baseColors[purpose])[variant]
      return isChecked ? { ...styles, ...styles.checked } : styles
    }),
  }
}
