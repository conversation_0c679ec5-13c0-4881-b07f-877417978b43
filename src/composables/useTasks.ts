import { ref, computed, onUnmounted } from 'vue'
import { useFirebase } from './useFirebase'
import {
  collection,
  query,
  onSnapshot,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  Timestamp,
  type Unsubscribe,
  type DocumentData,
  setDoc,
} from 'firebase/firestore'

interface TaskData {
  title: string // required
  details?: string // optional
  isUrgent: boolean // required
  isCompleted: boolean // required
  author: string // required
  authorEmail?: string
  assignedTo?: string | null
  dueDate: Timestamp | null
  completedBy?: string | null
  createdAt: Timestamp // required
  updatedAt?: Timestamp
  completedAt: Timestamp | null
}

interface CreateTaskData {
  title: string
  details?: string
  isUrgent: boolean
  isCompleted: boolean
  author: string
  authorEmail?: string
  assignedTo?: string | null
  dueDate: Date | null
}

interface UpdateTaskData {
  title?: string
  details?: string
  isUrgent?: boolean
  isCompleted?: boolean
  assignedTo?: string | null
  dueDate?: Date | null
}

class Task {
  id: string
  title: string
  details?: string
  isUrgent: boolean
  isCompleted: boolean
  author: string
  authorEmail?: string
  assignedTo: string | null
  dueDate: Date | null
  completedBy: string | null
  createdAt: Date
  updatedAt: Date
  completedAt: Date | null

  constructor(id: string, data: TaskData) {
    this.id = id
    this.title = data.title
    this.details = data.details
    this.isUrgent = data.isUrgent
    this.isCompleted = data.isCompleted
    this.author = data.author
    this.authorEmail = data.authorEmail
    this.assignedTo = data.assignedTo || null
    this.dueDate = data.dueDate ? data.dueDate.toDate() : null
    this.completedBy = data.completedBy || null
    this.createdAt = data.createdAt ? data.createdAt.toDate() : new Date()
    this.updatedAt = data.updatedAt ? data.updatedAt.toDate() : new Date()
    this.completedAt = data.completedAt ? data.completedAt.toDate() : null
  }

  static fromFirestore(id: string, data: TaskData): Task {
    return new Task(id, data)
  }
}

// Type guard to check if data matches TaskData structure
function isTaskData(data: DocumentData): data is TaskData {
  return (
    typeof data.title === 'string' &&
    typeof data.isUrgent === 'boolean' &&
    typeof data.isCompleted === 'boolean' &&
    typeof data.author === 'string' &&
    'createdAt' in data
  )
}

export function useTasks() {
  const { db, currentUser } = useFirebase()

  const tasks = ref<Task[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  let unsubscribe: Unsubscribe | null = null

  // Computed tasks
  const incompleteTasks = computed(() =>
    tasks.value.filter(task => !task.isCompleted)
  )

  const urgentTasks = computed(() =>
    incompleteTasks.value
      .filter(task => task.isUrgent)
      .sort((a, b) => {
        // Sort by due date if available
        if (a.dueDate && b.dueDate) {
          return a.dueDate.getTime() - b.dueDate.getTime()
        }
        // Tasks with due dates come before tasks without
        if (a.dueDate) return -1
        if (b.dueDate) return 1
        // If no due dates, sort by creation date
        return b.createdAt.getTime() - a.createdAt.getTime()
      }),
  )

  const pendingTasks = computed(() =>
    incompleteTasks.value
      .filter(task => !task.isUrgent)
      .sort((a, b) => {
        // Sort by due date if available
        if (a.dueDate && b.dueDate) {
          return a.dueDate.getTime() - b.dueDate.getTime()
        }
        // Tasks with due dates come before tasks without
        if (a.dueDate) return -1
        if (b.dueDate) return 1
        // If no due dates, sort by creation date
        return b.createdAt.getTime() - a.createdAt.getTime()
      }),
  )

  const completedTasks = computed(() =>
    tasks.value
      .filter(task => task.isCompleted)
      .sort(
        (a, b) =>
          (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0),
      ),
  )

  // Subscribe to tasks
  const subscribeToTasks = () => {
    loading.value = true
    error.value = null

    // Clear any existing subscription
    if (unsubscribe) {
      unsubscribe()
    }

    const tasksRef = collection(db, 'tasks')

    // Get all tasks for now to debug
    const tasksQuery = query(tasksRef)

    unsubscribe = onSnapshot(
      tasksQuery,
      snapshot => {
        tasks.value = snapshot.docs
          .filter(doc => isTaskData(doc.data()))
          .map(doc => Task.fromFirestore(doc.id, doc.data() as TaskData))
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        loading.value = false
      },
      err => {
        console.error('Error fetching tasks:', err)
        error.value = 'Failed to fetch tasks'
        loading.value = false
      },
    )
  }

  // Create a new task
  const createTask = async (taskData: CreateTaskData) => {
    if (!currentUser.value?.email) {
      throw new Error('User not authenticated')
    }

    // Generate a timestamp-based ID in format: YYYY-MM-DDTHH-mm-ss-SSS
    const now = new Date()
    const taskId = now.toISOString()

    try {
      const tasksRef = doc(db, 'tasks', taskId)
      const newTaskData = {
        ...taskData,
        dueDate: taskData.dueDate ? Timestamp.fromDate(taskData.dueDate) : null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        completedAt: null,
        completedBy: null,
      }
      await setDoc(tasksRef, newTaskData)
    } catch (err) {
      console.error('Error creating task:', err)
      throw new Error('Failed to create task')
    }
  }

  // Update a task
  const updateTask = async (taskId: string, taskData: UpdateTaskData) => {
    if (!currentUser.value?.email) {
      throw new Error('User not authenticated')
    }

    try {
      const taskRef = doc(db, 'tasks', taskId)
      const updateData = {
        ...taskData,
        dueDate: taskData.dueDate ? Timestamp.fromDate(taskData.dueDate) : null,
        updatedAt: serverTimestamp(),
      }
      await updateDoc(taskRef, updateData)
    } catch (err) {
      console.error('Error updating task:', err)
      throw new Error('Failed to update task')
    }
  }

  // Toggle task completion
  const toggleTaskCompletion = async (taskId: string, isCompleted: boolean) => {
    if (!currentUser.value?.email) {
      throw new Error('User not authenticated')
    }

    try {
      const taskRef = doc(db, 'tasks', taskId)
      await updateDoc(taskRef, {
        isCompleted,
        completedAt: isCompleted ? serverTimestamp() : null,
        completedBy: isCompleted ? currentUser.value.email : null,
        updatedAt: serverTimestamp(),
      })
    } catch (err) {
      console.error('Error updating task completion:', err)
      throw new Error('Failed to update task')
    }
  }

  // Toggle task urgency
  const toggleTaskUrgency = async (taskId: string, isUrgent: boolean) => {
    if (!currentUser.value?.email) {
      throw new Error('User not authenticated')
    }

    try {
      const taskRef = doc(db, 'tasks', taskId)
      await updateDoc(taskRef, {
        isUrgent,
        updatedAt: serverTimestamp(),
      })
    } catch (err) {
      console.error('Error updating task urgency:', err)
      throw new Error('Failed to update task')
    }
  }

  // Delete a task
  const deleteTask = async (taskId: string) => {
    if (!currentUser.value?.email) {
      throw new Error('User not authenticated')
    }

    try {
      const taskRef = doc(db, 'tasks', taskId)
      await deleteDoc(taskRef)
    } catch (err) {
      console.error('Error deleting task:', err)
      throw new Error('Failed to delete task')
    }
  }

  // Cleanup
  const cleanup = () => {
    if (unsubscribe) {
      unsubscribe()
      unsubscribe = null
    }
  }

  onUnmounted(() => {
    cleanup()
  })

  return {
    tasks,
    urgentTasks,
    pendingTasks,
    completedTasks,
    incompleteTasks,
    loading,
    error,
    subscribeToTasks,
    createTask,
    updateTask,
    toggleTaskCompletion,
    toggleTaskUrgency,
    deleteTask,
    cleanup,
  }
}
