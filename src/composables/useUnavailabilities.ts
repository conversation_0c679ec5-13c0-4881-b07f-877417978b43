import { ref, computed, type Ref, type ComputedRef } from 'vue'
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  Timestamp,
  orderBy,
  type DocumentData,
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import {
  Unavailability,
  type UnavailabilityData,
} from '../models/Unavailability'
import { useArtists } from './useArtists'
import type { Artist, ArtistPhoto, ArtistAvailability } from '../models/Artist'

interface UnavailabilityItem extends DocumentData {
  id: string
  artistId: string
  startDate: Timestamp
  endDate: Timestamp
  reason?: string
  status?: 'active' | 'cancelled'
  createdAt?: Timestamp
  updatedAt?: Timestamp
}

interface UseUnavailabilitiesReturn {
  unavailabilities: Ref<Unavailability[]>
  error: Ref<string | null>
  isLoading: Ref<boolean>
  fetchUnavailabilities: (startDate: Date, endDate: Date) => Promise<void>
  createUnavailability: (data: UnavailabilityData) => Promise<string>
  unavailabilitiesForDay: ComputedRef<(date: Date) => Unavailability[]>
  getUnavailableArtists: ComputedRef<
    (date: Date) => Array<{
      id: string | null
      firstName: string
      lastName: string
      stageName: string
      instruments: string[]
      photoUrl: string | null
      otherPhotoUrls: string[]
      photo: ArtistPhoto | null
      availability: ArtistAvailability[]
      unavailabilityReason: string
    }>
  >
}

export function useUnavailabilities(): UseUnavailabilitiesReturn {
  const { db } = useFirebase()
  const { artists } = useArtists()
  const unavailabilities = ref<Unavailability[]>([])
  const error = ref<string | null>(null)
  const isLoading = ref(false)

  const fetchUnavailabilityDetails = async (
    items: UnavailabilityItem[],
  ): Promise<Unavailability[]> => {
    return items.map(
      data =>
        new Unavailability({
          id: data.id,
          artistId: data.artistId,
          startDate: data.startDate?.toDate(),
          endDate: data.endDate?.toDate(),
          reason: data.reason,
          status: data.status || 'active',
        }),
    )
  }

  const fetchUnavailabilities = async (
    startDate: Date,
    endDate: Date,
  ): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null

      // Convert dates to Firestore Timestamps for query
      const startTimestamp = Timestamp.fromDate(startDate)
      const endTimestamp = Timestamp.fromDate(endDate)

      console.log('Fetching unavailabilities between:', { startDate, endDate })

      // Modify query to get all active unavailabilities that might overlap
      const q = query(
        collection(db, 'artistAvailability'),
        where('status', '==', 'active'),
        where('endDate', '>=', startTimestamp),
        orderBy('endDate'),
        orderBy('startDate'),
      )

      const snapshot = await getDocs(q)
      console.log(
        'Raw snapshot:',
        snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })),
      )

      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as UnavailabilityItem[]

      console.log('Raw unavailability items:', items)

      // Simplify the filtering
      const filteredItems = items.filter(item => {
        // Ensure we're checking properties that exist
        if (!item.startDate || !item.endDate) return false

        const start = item.startDate?.toDate()
        const end = item.endDate?.toDate()
        const isValid = start && end && start <= endDate && end >= startDate
        console.log('Filtering item:', { start, end, isValid })
        return isValid
      })

      console.log('Filtered unavailability items:', filteredItems)

      unavailabilities.value = await fetchUnavailabilityDetails(filteredItems)
      console.log('Final processed unavailabilities:', unavailabilities.value)
    } catch (err) {
      console.error('Error fetching unavailabilities:', err)
      error.value =
        err instanceof Error ? err.message : 'Unknown error occurred'
    } finally {
      isLoading.value = false
    }
  }

  const createUnavailability = async (
    data: UnavailabilityData,
  ): Promise<string> => {
    try {
      isLoading.value = true
      error.value = null

      const docRef = await addDoc(collection(db, 'artistAvailability'), {
        ...data,
        status: 'active',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      })

      return docRef.id
    } catch (err) {
      console.error('Error creating unavailability:', err)
      error.value =
        err instanceof Error ? err.message : 'Unknown error occurred'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const unavailabilitiesForDay = computed(() => (date: Date) => {
    return unavailabilities.value.filter(unavail => {
      const startDate = new Date(unavail.startDate)
      const endDate = new Date(unavail.endDate)
      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)
      return date >= startDate && date <= endDate
    })
  })

  // New utility function to get unavailable artists for a date
  const getUnavailableArtists = computed(() => (date: Date) => {
    console.log('Checking unavailabilities for date:', date)
    console.log('Current unavailabilities:', unavailabilities.value)
    console.log('Available artists:', artists.value)

    if (!unavailabilities.value?.length || !artists.value?.length) {
      console.log('No unavailabilities or artists data')
      return []
    }

    // Get all unavailabilities that include this date
    const relevantUnavailabilities = unavailabilities.value.filter(unavail => {
      const startDate = new Date(unavail.startDate)
      const endDate = new Date(unavail.endDate)
      const checkDate = new Date(date)

      // Set all times to noon to avoid timezone issues
      startDate.setHours(12, 0, 0, 0)
      endDate.setHours(12, 0, 0, 0)
      checkDate.setHours(12, 0, 0, 0)

      const isRelevant = checkDate >= startDate && checkDate <= endDate
      console.log('Checking unavailability:', {
        startDate,
        endDate,
        checkDate,
        isRelevant,
      })
      return isRelevant
    })

    console.log('Relevant unavailabilities:', relevantUnavailabilities)

    // Get unique artist IDs from the unavailabilities
    const unavailableArtistIds = new Set(
      relevantUnavailabilities.map(u => u.artistId),
    )

    console.log('Unavailable artist IDs:', [...unavailableArtistIds])

    // Return array of artist objects who are unavailable
    const unavailableArtists = artists.value
      .filter(artist => artist.id && unavailableArtistIds.has(artist.id))
      .map(artist => ({
        ...artist,
        unavailabilityReason:
          relevantUnavailabilities.find(u => u.artistId === artist.id)
            ?.reason || 'Unavailable',
      }))

    console.log('Final unavailable artists:', unavailableArtists)
    return unavailableArtists
  })

  return {
    unavailabilities,
    error,
    isLoading,
    fetchUnavailabilities,
    createUnavailability,
    unavailabilitiesForDay,
    getUnavailableArtists,
  }
}
