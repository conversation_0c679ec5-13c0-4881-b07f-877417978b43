import { ref, computed } from 'vue'
import { Timestamp } from 'firebase/firestore'
import { Event } from '@/models/Event'
import type { EventStatus } from '@/types/models'
import { useDateTime } from './useDateTime'

const { getNextAvailable8PM } = useDateTime()

export const MIN_DURATION = 60 // minimum duration in minutes

type ValidationError = {
  field: string
  message: string
}

export function useEventForm(initialData?: Partial<Event>) {
  const defaultValues: Partial<Event> = {
    title: '',
    description: '',
    when: Timestamp.fromDate(getNextAvailable8PM()),
    duration: 180,
    venue: '',
    venueDetails: null,
    acts: ['daves-roy-orbison', 'human-jukebox'],
    actDetails: [],
    cta: null,
    notes: {
      fee: { amount: null, paid: false, date: null },
      deposit: { amount: null, paid: false, date: null },
      agent: null,
    },
    isPrivate: false,
    status: initialData?.status || 'draft',
  }

  const formData = ref<Partial<Event>>({
    ...defaultValues,
    ...initialData,
  })

  const errors = ref<ValidationError[]>([])
  const isDirty = ref(false)

  const validate = () => {
    const newErrors: ValidationError[] = []

    if (!formData.value.acts?.length) {
      newErrors.push({
        field: 'acts',
        message: 'At least one act is required',
      })
    }

    if (!formData.value.venue) {
      newErrors.push({
        field: 'venue',
        message: 'Venue is required',
      })
    }

    const eventDate =
      formData.value.when instanceof Timestamp
        ? formData.value.when.toDate()
        : formData.value.when

    const minDate = new Date()
    minDate.setHours(minDate.getHours() + 5)

    if (eventDate && eventDate < minDate) {
      newErrors.push({
        field: 'when',
        message: 'Event must be at least 5 hours in the future',
      })
    }

    if (!formData.value.duration || formData.value.duration < 60) {
      newErrors.push({
        field: 'duration',
        message: 'Duration must be at least 60 minutes',
      })
    }

    errors.value = newErrors
    return errors.value.length === 0
  }

  const getFieldError = (field: string) => {
    return errors.value.find(error => error.field === field)?.message
  }

  const updateField = <T extends keyof Event>(field: T, value: Event[T]) => {
    formData.value[field] = value
    isDirty.value = true
    validate()
  }

  const isValid = computed(() => validate())

  return {
    formData,
    errors,
    isDirty,
    isValid,
    validate,
    getFieldError,
    updateField,
  }
}
