import { useDateFormat } from '@vueuse/core'

const TIME_FORMAT = 'HH:mm'
const DATE_FORMAT = 'ddd, Do MMM'
const DATE_YEAR_FORMAT = 'ddd, Do MMM YY'

interface DateWithToDate {
  toDate: () => Date
}

function showYear(date: Date): string {
  return date.getFullYear() !== new Date().getFullYear()
    ? DATE_YEAR_FORMAT
    : DATE_FORMAT
}

function isDateWithToDate(
  value: Date | DateWithToDate,
): value is DateWithToDate {
  return 'toDate' in value && typeof value.toDate === 'function'
}

export function useDateTime() {
  const isToday = (date: Date): boolean => {
    if (!date) return false

    return date.toDateString() === new Date().toDateString()
  }

  const isPast = (date: Date): boolean => {
    if (!date) return false

    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today
  }

  const formatEventDateTime = (timestamp: Date | DateWithToDate): string => {
    if (!timestamp) return ''
    const date = isDateWithToDate(timestamp) ? timestamp.toDate() : timestamp
    return useDateFormat(date, `${showYear(date)} ${TIME_FORMAT}`).value
  }

  const formatTime = (timestamp: Date | DateWithToDate): string => {
    if (!timestamp) return ''
    const date = isDateWithToDate(timestamp) ? timestamp.toDate() : timestamp
    return useDateFormat(date, TIME_FORMAT).value
  }

  const formatDate = (timestamp: Date | DateWithToDate): string => {
    if (!timestamp) return ''
    const date = isDateWithToDate(timestamp) ? timestamp.toDate() : timestamp
    return useDateFormat(date, showYear(date)).value
  }

  const getEndTime = (
    startTime: Date | DateWithToDate,
    durationMinutes: number,
  ): string => {
    if (!startTime || !durationMinutes) return ''
    const startDate = isDateWithToDate(startTime)
      ? startTime.toDate()
      : startTime
    const endTime = new Date(startDate)
    endTime.setMinutes(endTime.getMinutes() + durationMinutes)
    return useDateFormat(endTime, TIME_FORMAT).value
  }

  const getNextAvailable8PM = (leadTimeHours = 5): Date => {
    const now = new Date()
    const next8PM = new Date(now)
    next8PM.setHours(20, 0, 0, 0)

    // If 8PM today is less than 8 hours away, move to tomorrow
    const minTime = new Date(now)
    minTime.setHours(minTime.getHours() + leadTimeHours)

    if (next8PM < minTime) {
      next8PM.setDate(next8PM.getDate() + 1)
    }

    return next8PM
  }

  return {
    isToday,
    isPast,
    formatEventDateTime,
    formatTime,
    formatDate,
    getEndTime,
    getNextAvailable8PM,
  }
}
