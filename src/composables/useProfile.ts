import { ref } from 'vue'
import { useFirebase } from './useFirebase'
import { useUsers } from './useUsers'
import { useActivityLog } from './useActivityLog'
import type { ActivityType } from './useActivityLog'
import { useToast } from 'vue-toastification'
import {
  updateEmail,
  updatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider,
  sendEmailVerification,
  deleteUser,
  updateProfile,
} from 'firebase/auth'
import {
  doc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  collection,
  addDoc,
  getDoc,
  getDocs,
  Timestamp,
} from 'firebase/firestore'
import {
  ref as storageRef,
  uploadBytes,
  getDownloadURL,
} from 'firebase/storage'
import type {
  UserData,
  PasswordUpdateData,
  PhotoUploadData,
} from '@/types/user'

export function useProfile() {
  const { db, auth, storage } = useFirebase()
  const { updateUserProfile } = useUsers()
  const { logActivity, ACTIVITY_TYPES } = useActivityLog()
  const toast = useToast()

  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Update password
  async function updateUserPassword(
    data: PasswordUpdateData,
  ): Promise<boolean> {
    if (!auth.currentUser) return false

    try {
      isLoading.value = true
      error.value = null

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email!,
        data.currentPassword,
      )
      await reauthenticateWithCredential(auth.currentUser, credential)

      // Update password
      await updatePassword(auth.currentUser, data.newPassword)

      // Log activity
      await logActivity('PASSWORD_CHANGED' as ActivityType)

      toast.success('Password updated successfully')
      return true
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to update password'
      toast.error(error.value)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Update email
  async function updateUserEmail(
    newEmail: string,
    password: string,
  ): Promise<boolean> {
    if (!auth.currentUser) return false

    try {
      isLoading.value = true
      error.value = null

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email!,
        password,
      )
      await reauthenticateWithCredential(auth.currentUser, credential)

      const oldEmail = auth.currentUser.email

      // Update email in Auth
      await updateEmail(auth.currentUser, newEmail)

      // Update email in Firestore
      const userRef = doc(db, 'users', auth.currentUser.uid)
      await updateDoc(userRef, {
        email: newEmail,
        updatedAt: serverTimestamp(),
      })

      // Send verification email
      await sendEmailVerification(auth.currentUser)

      // Log activity
      await logActivity('EMAIL_UPDATED' as ActivityType, {
        oldEmail,
        newEmail,
      })

      toast.success(
        'Email updated successfully. Please verify your new email address.',
      )
      return true
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to update email'
      toast.error(error.value)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Upload profile photo
  async function uploadProfilePhoto(
    data: PhotoUploadData,
  ): Promise<string | null> {
    if (!auth.currentUser) return null

    try {
      isLoading.value = true
      error.value = null

      // Create a reference to the storage location
      const photoRef = storageRef(
        storage,
        `profile-photos/${auth.currentUser.uid}`,
      )

      // Upload the file
      const snapshot = await uploadBytes(photoRef, data.file)

      // Get the download URL
      const downloadURL = await getDownloadURL(snapshot.ref)

      // Update user profile
      await updateProfile(auth.currentUser, {
        photoURL: downloadURL,
      })

      // Update in Firestore
      const userRef = doc(db, 'users', auth.currentUser.uid)
      await updateDoc(userRef, {
        photoURL: downloadURL,
        updatedAt: serverTimestamp(),
      })

      // Log activity
      await logActivity('PHOTO_UPDATED' as ActivityType)

      toast.success('Profile photo updated successfully')
      return downloadURL
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to upload photo'
      toast.error(error.value)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // Send verification email
  async function resendVerificationEmail(): Promise<boolean> {
    if (!auth.currentUser) return false

    try {
      isLoading.value = true
      error.value = null

      await sendEmailVerification(auth.currentUser)

      // Log activity
      await logActivity('EMAIL_VERIFIED' as ActivityType)

      toast.success('Verification email sent')
      return true
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to send verification email'
      toast.error(error.value)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Delete account
  async function deleteAccount(password: string): Promise<boolean> {
    if (!auth.currentUser) return false

    try {
      isLoading.value = true
      error.value = null

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email!,
        password,
      )
      await reauthenticateWithCredential(auth.currentUser, credential)

      // Log activity before deletion
      await logActivity('ACCOUNT_DELETED' as ActivityType)

      // Delete from Firestore first
      await deleteDoc(doc(db, 'users', auth.currentUser.uid))

      // Delete from Auth
      await deleteUser(auth.currentUser)

      toast.success('Account deleted successfully')
      return true
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to delete account'
      toast.error(error.value)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Export data
  async function exportUserData(): Promise<Blob | null> {
    if (!auth.currentUser) return null

    try {
      isLoading.value = true
      error.value = null

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid))
      const userData = userDoc.data() as UserData

      // Get activity log
      const activitySnapshot = await getDocs(
        collection(doc(db, 'users', auth.currentUser.uid), 'activity'),
      )
      const activities = activitySnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
      }))

      // Combine data
      const exportData = {
        userData,
        activities,
        exportDate: new Date().toISOString(),
      }

      // Create blob
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      })

      // Log activity
      await logActivity('DATA_EXPORTED' as ActivityType)

      return blob
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export data'
      toast.error(error.value)
      return null
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    updateUserPassword,
    updateUserEmail,
    uploadProfilePhoto,
    resendVerificationEmail,
    deleteAccount,
    exportUserData,
  }
}
