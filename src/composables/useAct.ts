import { ref } from 'vue'
import type { Ref } from 'vue'
import {
  doc,
  getDoc,
  updateDoc,
  Timestamp,
  DocumentSnapshot,
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { Act } from '../models/Act'

const { db } = useFirebase()

interface ActData {
  id: string
  artists?: Record<string, string[]>
  [key: string]: any
}

// Create a cache to store act data
const actCache = new Map<string, ActData>()

// New function to batch load acts
export async function loadActsInBatch(actIds: string[]): Promise<ActData[]> {
  const uncachedIds = actIds.filter(id => !actCache.has(id))

  if (uncachedIds.length === 0) {
    return actIds.map(id => actCache.get(id)!)
  }

  await Promise.all(
    uncachedIds.map(async actId => {
      try {
        const docRef = doc(db, 'acts', actId)
        const docSnap = await getDoc(docRef)
        if (docSnap.exists()) {
          const actData: ActData = {
            id: docSnap.id,
            ...docSnap.data(),
          }
          actCache.set(actId, actData)
        }
      } catch (e) {
        console.error('Error loading act:', e)
      }
    }),
  )

  return actIds.map(id => actCache.get(id)!).filter(Boolean)
}

interface UseActReturn {
  act: Ref<ActData | null>
  isLoadingAct: Ref<boolean>
  error: Ref<string | null>
  updateAct: (field: string, value: any) => Promise<void>
  clearCache: () => void
}

export function useAct(actId: string): UseActReturn {
  const act: Ref<ActData | null> = ref(null)
  const isLoadingAct = ref(true)
  const error = ref<string | null>(null)

  const fetchAct = async () => {
    try {
      // Check if we have a cached version
      if (actCache.has(actId)) {
        act.value = actCache.get(actId)!
        isLoadingAct.value = false
        return
      }

      isLoadingAct.value = true
      error.value = null

      const docRef = doc(db, 'acts', actId)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const actData: ActData = {
          id: docSnap.id,
          ...docSnap.data(),
        }
        act.value = actData
        // Store in cache
        actCache.set(actId, actData)
      } else {
        throw new Error('Act not found')
      }
    } catch (e) {
      const err = e as Error
      error.value = err.message
      console.error('Error loading act:', err)
    } finally {
      isLoadingAct.value = false
    }
  }

  const updateAct = async (field: string, value: any): Promise<void> => {
    try {
      const docRef = doc(db, 'acts', actId)
      await updateDoc(docRef, {
        [field]: value,
        updatedAt: Timestamp.now(),
      })

      if (field === 'artistIds') {
        // If we're updating artists, make sure we maintain the existing instruments
        const currentArtists = act.value?.artists || {}
        const newArtists: Record<string, string[]> = {}
        value.forEach((artistId: string) => {
          newArtists[artistId] = currentArtists[artistId] || []
        })
        await updateDoc(docRef, {
          artists: newArtists,
          updatedAt: Timestamp.now(),
        })
      }

      // Update the cache after successful update
      if (actCache.has(actId)) {
        const cachedAct = actCache.get(actId)!
        actCache.set(actId, {
          ...cachedAct,
          [field]: value,
          updatedAt: Timestamp.now(),
        })
      }

      // Refresh the act data
      await fetchAct()
    } catch (e) {
      console.error('Error updating act:', e)
      throw e
    }
  }

  // Clear cache for this act
  const clearCache = () => {
    actCache.delete(actId)
  }

  // Fetch initially
  fetchAct()

  return {
    act,
    isLoadingAct,
    error,
    updateAct,
    clearCache,
  }
}

interface UseActClassReturn {
  act: Ref<Act | null>
  fetchAct: () => Promise<void>
}

export function useActClass(actId: string): UseActClassReturn {
  const act: Ref<Act | null> = ref(null)

  const fetchAct = async () => {
    const docSnap = await getDoc(doc(db, 'acts', actId))
    if (docSnap.exists()) {
      act.value = Act.fromFirestore(docSnap)
    }
  }

  return { act, fetchAct }
}
