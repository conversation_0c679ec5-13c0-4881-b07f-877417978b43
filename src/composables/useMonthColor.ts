export const useMonthColor = () => {
  // Month colors use short hex codes.
  const monthColors = [
    { regular: '#F532', active: '#F008', textOnActive: '#FFF' }, // Red
    { regular: '#3F52', active: '#0F08', textOnActive: 'currentColor' }, // Green
    { regular: '#35F2', active: '#00F8', textOnActive: '#FFF' }, // Blue
  ]

  const monthColor = (month: number, isActive = false) => {
    const colorSet = monthColors[month % 3]
    return isActive ? colorSet.active : colorSet.regular
  }

  const textColor = (month: number, isActive = false) => {
    if (!isActive) return undefined
    const colorSet = monthColors[month % 3]
    return colorSet.textOnActive
  }

  return {
    monthColor,
    textColor,
  }
}
