import { Event } from '@/models/Event'

export function useEventFormAdapter() {
  const transformCta = (value: string): Event['cta'] => {
    const [type, text, url, price] = value.split('|')
    return {
      type: type as 'fb' | 'ticket',
      text,
      url,
      price: price || null,
    }
  }

  const transformNotes = (
    category: 'fee' | 'deposit' | 'agent',
    field: string,
    value: string,
  ): string | number | null => {
    type TransformFn = (v: string) => string | number | null

    type Transforms = {
      fee: {
        amount: TransformFn
      }
      deposit: {
        amount: TransformFn
      }
      agent: string | null
    }

    const transforms: Transforms = {
      fee: {
        amount: (v: string | number) => (v ? Number(v) : null),
      },
      deposit: {
        amount: (v: string | number) => (v ? Number(v) : null),
      },
      agent: value as string | null,
    }

    const transform =
      // Handle agent category separately since it's not an object
      category === 'agent'
        ? transforms.agent
        : transforms[category][
            field as keyof (typeof transforms)[typeof category]
          ]
    return (transform as TransformFn)(value)
  }

  const transformDuration = (value: string): number => {
    const [hours, minutes] = value.split(':').map(Number)
    return !isNaN(hours) && !isNaN(minutes) ? hours * 60 + minutes : 180
  }

  return {
    transformCta,
    transformNotes,
    transformDuration,
  } as const
}
