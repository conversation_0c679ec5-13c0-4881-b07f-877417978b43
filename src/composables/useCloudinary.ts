import { ref } from 'vue'

const CLOUDINARY_CLOUD_NAME = 'dave-collison'
const CLOUDINARY_UPLOAD_URL = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`

export function useCloudinary() {
  const isUploading = ref(false)
  const uploadError = ref<string | null>(null)

  async function uploadImage(file: File, uploadPreset: string) {
    isUploading.value = true
    uploadError.value = null

    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_preset', uploadPreset)

    try {
      const response = await fetch(CLOUDINARY_UPLOAD_URL, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Upload error details:', errorData)
        throw new Error(`Upload failed: ${errorData.error?.message || 'Unknown error'}`)
      }

      const data = await response.json()
      return data.secure_url
    } catch (error) {
      console.error('Cloudinary upload error:', error)
      uploadError.value = error instanceof Error ? error.message : 'Upload failed'
      throw error
    } finally {
      isUploading.value = false
    }
  }

  return {
    isUploading,
    uploadError,
    uploadImage
  }
}
