import { ref } from 'vue'
import { useRouter } from 'vue-router'

interface EntityCardOptions {
  entityType: string;
  entity: Record<string, any>;
  onDelete?: () => Promise<void>;
}

/**
 * Composable for handling common entity card functionality
 * @param options - Configuration options
 * @returns Card functionality and state
 */
export function useEntityCard({ entityType, entity, onDelete }: EntityCardOptions) {
  const router = useRouter()
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // Image handling
  const imageLoaded = ref(false)
  const imageError = ref(false)

  function handleImageLoad(): void {
    imageLoaded.value = true
  }

  function handleImageError(): void {
    imageError.value = true
  }

  // Navigation
  function viewEntity(): void {
    router.push({ name: `${entityType}s.show`, params: { id: entity.id } })
  }

  function editEntity(): void {
    router.push({ name: `${entityType}s.edit`, params: { id: entity.id } })
  }

  // Delete handling
  async function deleteEntity(): Promise<void> {
    if (!confirm(`Are you sure you want to delete this ${entityType}?`)) {
      return
    }

    isLoading.value = true
    error.value = null

    try {
      if (onDelete) {
        await onDelete()
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error(`Error deleting ${entityType}:`, err)
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    imageLoaded,
    imageError,
    handleImageLoad,
    handleImageError,
    viewEntity,
    editEntity,
    deleteEntity
  }
}
