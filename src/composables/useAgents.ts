import { ref } from 'vue'
import { collection, onSnapshot } from 'firebase/firestore'
import { useFirebase } from './useFirebase'

export function useAgents() {
  const { db } = useFirebase()
  const agents = ref()
  let unsubscribe: (() => void) | null = null

  const subscribeToAgents = () => {
    if (unsubscribe) return

    unsubscribe = onSnapshot(collection(db, 'agents'), (snapshot) => {
      agents.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    })
  }

  const cleanup = () => {
    if (unsubscribe) {
      unsubscribe()
      unsubscribe = null
    }
  }

  return {
    agents,
    subscribeToAgents,
    cleanup
  }
}