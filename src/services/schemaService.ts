/**
 * Save updated schema back to the file system
 * @param {string} collectionName - Name of the collection
 * @param {Schema} schema - Updated schema object
 * @returns {Promise<SaveSchemaResponse>} Response from the server
 * @throws {SchemaServiceError} If the save operation fails
 */
interface Schema {
  [key: string]: unknown
}

interface SaveSchemaResponse {
  success: boolean
  message?: string
}

interface SaveSchemaRequest {
  collection: string
  schema: Schema
}

class SchemaServiceError extends Error {
  constructor(message: string, public readonly cause?: unknown) {
    super(message)
    this.name = 'SchemaServiceError'
  }
}

export async function saveSchema(
  collectionName: string,
  schema: Schema
): Promise<SaveSchemaResponse> {
  const request: SaveSchemaRequest = {
    collection: collectionName,
    schema
  }

  try {
    const response = await fetch('/api/schema/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json() as SaveSchemaResponse
    return data
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error occurred'
    console.error('Error saving schema:', error)
    throw new SchemaServiceError(`Failed to save schema: ${message}`, error)
  }
}
