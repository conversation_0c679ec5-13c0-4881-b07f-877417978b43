import type { Router, RouteLocationNormalizedLoaded } from 'vue-router'

// Create a simple navigation service
let router: Router | undefined

interface NavigationService {
  setRouter(routerInstance: Router): void
  navigate(path: string): void
  getCurrentRoute(): RouteLocationNormalizedLoaded | undefined
}

export const navigationService: NavigationService = {
  setRouter(routerInstance: Router): void {
    router = routerInstance
  },

  navigate(path: string): void {
    if (!router) {
      console.error('Router is not set')
      return
    }
    router.push(path)
  },

  getCurrentRoute(): RouteLocationNormalizedLoaded | undefined {
    return router?.currentRoute.value
  }
}
