/* @link https://utopia.fyi/type/calculator?c=320,16,1.125,1240,22,1.25,5,2,&s=0.75|0.5|0.25,1.5|2|3|4|6,s-l&g=s,l,xl,12 */

:root {
  --step--3: clamp(0.7rem, 0.64rem + 0.07cqi, 0.76rem);
  --step--2: clamp(0.7901rem, 0.7589rem + 0.1563cqi, 0.88rem);
  --step--1: clamp(0.8889rem, 0.8155rem + 0.3671cqi, 1.1rem);
  --step-0: clamp(1rem, 0.8696rem + 0.6522cqi, 1.375rem);
  --step-1: clamp(1.125rem, 0.9185rem + 1.0326cqi, 1.7188rem);
  --step-2: clamp(1.2656rem, 0.9586rem + 1.5353cqi, 2.1484rem);
  --step-3: clamp(1.4238rem, 0.985rem + 2.<PERSON><PERSON><PERSON>, 2.6855rem);
  --step-4: clamp(1.6018rem, 0.9913rem + 3.0524cqi, 3.3569rem);
  --step-5: clamp(1.802rem, 0.9693rem + 4.1637cqi, 4.1962rem);
}
