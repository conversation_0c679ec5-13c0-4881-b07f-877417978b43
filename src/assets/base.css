/* Add this import at the very top of the file */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700;800&display=swap');

:root {
  /* Border radius tokens */
  --radius-xs: 0.25rem; /* 4px */
  --radius-s: 0.375rem; /* 6px */
  --radius-sm: 0.5rem; /* 8px */
  --radius-m: 0.75rem; /* 12px */
  --radius-l: 1rem; /* 16px */
  --radius-xl: 1.5rem; /* 24px */

  /* Default radius for general use */
  --radius: var(--radius-m);

  /* Spacing system aligned with typography scale */
  --space-3xs: var(--step--4, 0.375rem); /* 6px */
  --space-2xs: var(--step--3, 0.5rem); /* 8px */
  --space-xs: var(--step--2, 0.75rem); /* 12px */
  --space-s: var(--step--1, 1rem); /* 16px */
  --space-m: var(--step-0, 1.5rem); /* 24px */
  --space-l: var(--step-1, 2rem); /* 32px */
  --space-xl: var(--step-2, 3rem); /* 48px */
  --space-2xl: var(--step-3, 4rem); /* 64px */
  --space-3xl: var(--step-4, 6rem); /* 96px */

  /* Common spacing values for consistent gaps and padding */
  --space-gap-2xs: var(--space-3xs);
  --space-gap-xs: var(--space-2xs);
  --space-gap-s: var(--space-xs);
  --space-gap-m: var(--space-s);
  --space-gap-l: var(--space-m);
  --space-gap-xl: var(--space-l);

  /* Inset (padding) values */
  --space-inset-xs: var(--space-2xs);
  --space-inset-s: var(--space-xs);
  --space-inset-m: var(--space-s);
  --space-inset-l: var(--space-m);

  /* Transitions - Default quick transitions */
  --transition-duration-quick: 0.01s;
  --transition-duration-normal: 0.25s;
  --transition-duration-click: 0.1s;
  --transition-in: var(--transition-duration-quick) ease-in;
  --transition-out: var(--transition-duration-quick) ease-out;
  --transition-click-in: var(--transition-duration-quick) ease-in;
  --transition-click-out: var(--transition-duration-quick) ease-out;
}

/* Override for users who don't prefer reduced motion */
@media (prefers-reduced-motion: no-preference) {
  :root {
    --transition-in: var(--transition-duration-normal) ease-in;
    --transition-out: var(--transition-duration-normal) ease-out;
    --transition-click-in: var(--transition-duration-click) ease-in;
    --transition-click-out: var(--transition-duration-click) ease-in;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100dvh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.2s,
    background-color 0.2s;
  line-height: 1.5;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: var(--step-0);

  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1,
h2,
h3 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  letter-spacing: -0.02em;
  color: var(--color-heading);
}

h1 {
  font-size: var(--step-4);
  line-height: 1.1;
  font-weight: 800;
}

h2 {
  font-size: var(--step-3);
  line-height: 1.1;
}

h3 {
  font-size: var(--step-2);
  line-height: 1.2;
}

h4 {
  font-size: var(--step-1);
  line-height: 1.3;
}

h5 {
  font-size: var(--step-0);
  line-height: 1.5;
}

h6 {
  font-size: var(--step-0);
  font-style: italic;
  opacity: 0.8;
  line-height: 1.5;
}

:is(button, input) {
  font-size: inherit;
}

main:has(.align-top) {
  margin-top: 0;
  margin-bottom: auto;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-background-soft);
  border-radius: 1rem;
  border: 1px solid var(--color-border);
}

.empty-state p {
  color: var(--color-text-muted);
  margin-bottom: 1rem;
}

.action-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  background-color: var(--color-accent);
  color: var(--color-background);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.action-button:hover {
  filter: brightness(1.1);
}

.danger {
  color: var(--color-text-danger);
}

.danger:hover {
  filter: brightness(0.9);
}

:where(a) {
  color: var(--color-brand);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: var(--space-3xs) var(--space-2xs);
  border-radius: 100vw;
}

:where(a:hover) {
  color: var(--color-brand-dark);
  background-color: var(--color-surface);
}

:where(a:active) {
  color: var(--color-brand-dark);
  background-color: var(--color-surface-soft);
}

:where(a:focus-visible) {
  outline: 2px solid var(--color-brand);
  outline-offset: 2px;
}

/* Button-style links */
:where(a[role='button']) {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2xs);
  background-color: var(--color-brand);
  color: var(--color-background);
  padding: var(--space-2xs) var(--space-xs);
  border-radius: 100vw;
  font-weight: 600;
}

:where(a[role='button']:hover) {
  background-color: var(--color-brand-dark);
  color: var(--color-background);
}

:where(a[role='button']:active) {
  background-color: var(--color-brand-dark);
  transform: translateY(1px);
}

:where(.icon) {
  width: 1em;
  height: 1em;
}
