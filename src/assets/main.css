@import './utilities.css';
@import './colours.css';
@import './typography.css';
@import './base.css';

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  display: grid;
  min-height: 100dvh;
  grid-template-rows: 1fr auto;
}

#app {
  display: contents;
}

/* Links */
a {
  color: var(--color-text-muted);
  text-decoration: none;
}

@media (hover: hover) {
  :where(a:hover) {
    text-decoration: underline;
  }
}

/* Dividers */
hr {
  margin-block: 1rem;
  border-color: var(--color-border);
}

/* Forms */
form {
  display: grid;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  width: fit-content;
}

/* Form Elements */
:is(input, button) {
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: all 0.2s ease;
}

input:focus {
  outline: none;
  border-color: var(--color-brand);
  box-shadow: 0 0 0 3px var(--color-brand-soft);
}

/* Layout */
main {
  width: 100%;
  max-width: 1400px;
  margin: 7rem auto 0;
  padding: 0 2rem;
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

/* Utility Classes */
.flex {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Status Indicators */
.status-success {
  color: var(--color-success);
  background-color: var(--color-success-soft);
}

.status-warning {
  color: var(--color-warning);
  background-color: var(--color-warning-soft);
}

.status-danger {
  color: var(--color-danger);
  background-color: var(--color-danger-soft);
}

.status-info {
  color: var(--color-info);
  background-color: var(--color-info-soft);
}

/* Active Navigation */
.router-link-active {
  color: var(--color-brand);
  background-color: var(--color-brand-soft);
}
