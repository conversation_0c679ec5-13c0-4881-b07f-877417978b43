/* Colours based on hue with light, dark, mute, sof variations plus 2 levels of opacity */

/* With built in automatic light/dark mode */

:root {
  color-scheme: light dark;

  /* Core colors */
  --color-bg-0: light-dark(#d0d0d0, #202020);
  --color-bg-1: light-dark(#d8d8d8, #282828);
  --color-bg-2: light-dark(#dedede, #313131);
  --color-bg-3: light-dark(#e5e5e5, #3c3c3c);
  --color-bg-4: light-dark(#f0f0f0, #424242);

  --color-fg-0: light-dark(#181818, #f4f4f4);
  --color-fg-1: light-dark(#242424, #e0e0e0);
  --color-fg-2: light-dark(#303030, #d8d8d8);
  --color-fg-3: light-dark(#383838, #c8c8c8);
  --color-fg-4: light-dark(#3c3c3c, #c0c0c0);

  --white: #f4f4f4;
  --black: #181818;

  --fg: var(--color-fg-0);
  --bg: var(--color-bg-0);

  --fg-soft: rgb(from var(--fg) r g b / 0.8);
  --fg-mute: rgb(from var(--fg) r g b / 0.5);
  --bg-soft: rgb(from var(--bg) r g b / 0.8);
  --bg-mute: rgb(from var(--bg) r g b / 0.5);

  /* Light mode defaults */
  --color-background: var(--bg);
  --color-background-soft: rgb(from var(--bg) r g b / 0.8);
  --color-background-mute: rgb(from var(--bg) r g b / 0.5);

  --color-border: rgba(from var(--fg) r g b / 0.16);
  --color-border-hover: rgba(from var(--fg) r g b / 0.29);

  /* Brand Colors - Using HSL for better manipulation */
  --brand-hue: 28;
  --brand-saturation: 85%;
  --brand-lightness: 50%;

  /* Color Roles */
  --primary-hue: calc(var(--brand-hue) + 180);
  --secondary-hue: var(--brand-hue);
  --accent-hue: calc(var(--brand-hue) + 120);
  --warning-hue: 38; /* Warm yellow */
  --danger-hue: 348; /* Warm red */
  --success-hue: 142; /* Natural green */
  --info-hue: 200; /* Cool blue */

  /* Brand Colors */
  --color-brand: hsl(
    var(--brand-hue) var(--brand-saturation) var(--brand-lightness)
  );
  --color-brand-alpha: hsla(
    var(--brand-hue) var(--brand-saturation) var(--brand-lightness) / 0.6
  );
  --color-brand-lightest: hsl(
    var(--brand-hue) calc(var(--brand-saturation) - 30%)
      calc(var(--brand-lightness) + 30%)
  );
  --color-brand-light: hsl(
    var(--brand-hue) calc(var(--brand-saturation) - 20%)
      calc(var(--brand-lightness) + 15%)
  );
  --color-brand-light-alpha: hsla(
    var(--brand-hue) calc(var(--brand-saturation) - 20%)
      calc(var(--brand-lightness) + 15%) / 0.6
  );
  --color-brand-dark: hsl(
    var(--brand-hue) var(--brand-saturation) calc(var(--brand-lightness) - 15%)
  );
  --color-brand-dark-alpha: hsla(
    var(--brand-hue) var(--brand-saturation) calc(var(--brand-lightness) - 15%) /
      0.6
  );
  --color-brand-darkest: hsl(
    var(--brand-hue) calc(var(--brand-saturation) + 10%)
      calc(var(--brand-lightness) - 30%)
  );
  --color-brand-muted: hsl(
    var(--brand-hue) calc(var(--brand-saturation) - 30%) var(--brand-lightness)
  );
  --color-brand-soft: hsl(
    var(--brand-hue) calc(var(--brand-saturation) - 50%)
      calc(var(--brand-lightness) + 25%)
  );

  --color-heading: var(--fg);
  --color-text: rgb(from var(--fg) r g b / 0.9);
  --color-text-soft: rgb(from var(--fg) r g b / 0.7);
  --color-text-muted: rgb(from var(--fg) r g b / 0.5);

  --color-surface-01: var(--fg);

  /* Primary Colors */
  --color-primary: hsl(var(--primary-hue) 85% 45%);
  --color-primary-alpha: hsla(var(--primary-hue) 85% 45% / 0.6);
  --color-primary-lightest: hsl(var(--primary-hue) 65% 75%);
  --color-primary-light: hsl(var(--primary-hue) 75% 60%);
  --color-primary-light-alpha: hsla(var(--primary-hue) 75% 60% / 0.6);
  --color-primary-dark: hsl(var(--primary-hue) 90% 35%);
  --color-primary-dark-alpha: hsla(var(--primary-hue) 90% 35% / 0.6);
  --color-primary-darkest: hsl(var(--primary-hue) 95% 25%);
  --color-primary-muted: hsl(var(--primary-hue) 55% 45%);
  --color-primary-soft: hsl(var(--primary-hue) 35% 65%);

  /* Secondary Colors */
  --color-secondary: hsl(var(--secondary-hue) 70% 45%);
  --color-secondary-alpha: hsla(var(--secondary-hue) 70% 45% / 0.6);
  --color-secondary-lightest: hsl(var(--secondary-hue) 50% 75%);
  --color-secondary-light: hsl(var(--secondary-hue) 60% 60%);
  --color-secondary-light-alpha: hsla(var(--secondary-hue) 60% 60% / 0.6);
  --color-secondary-dark: hsl(var(--secondary-hue) 75% 35%);
  --color-secondary-dark-alpha: hsla(var(--secondary-hue) 75% 35% / 0.6);
  --color-secondary-darkest: hsl(var(--secondary-hue) 80% 25%);
  --color-secondary-muted: hsl(var(--secondary-hue) 40% 45%);
  --color-secondary-soft: hsl(var(--secondary-hue) 30% 65%);

  /* Accent Colors */
  --color-accent: hsl(var(--accent-hue) 80% 45%);
  --color-accent-alpha: hsla(var(--accent-hue) 80% 45% / 0.6);
  --color-accent-lightest: hsl(var(--accent-hue) 60% 75%);
  --color-accent-light: hsl(var(--accent-hue) 70% 60%);
  --color-accent-light-alpha: hsla(var(--accent-hue) 70% 60% / 0.6);
  --color-accent-dark: hsl(var(--accent-hue) 85% 35%);
  --color-accent-dark-alpha: hsla(var(--accent-hue) 85% 35% / 0.6);
  --color-accent-darkest: hsl(var(--accent-hue) 90% 25%);
  --color-accent-muted: hsl(var(--accent-hue) 50% 45%);
  --color-accent-soft: hsl(var(--accent-hue) 40% 65%);

  /* Warning Colors */
  --color-warning: hsl(var(--warning-hue) 95% 45%);
  --color-warning-alpha: hsla(var(--warning-hue) 95% 45% / 0.6);
  --color-warning-lightest: hsl(var(--warning-hue) 75% 75%);
  --color-warning-light: hsl(var(--warning-hue) 85% 60%);
  --color-warning-light-alpha: hsla(var(--warning-hue) 85% 60% / 0.6);
  --color-warning-dark: hsl(var(--warning-hue) 100% 35%);
  --color-warning-dark-alpha: hsla(var(--warning-hue) 100% 35% / 0.6);
  --color-warning-darkest: hsl(var(--warning-hue) 100% 25%);
  --color-warning-muted: hsl(var(--warning-hue) 65% 45%);
  --color-warning-soft: hsl(var(--warning-hue) 45% 65%);

  /* Danger Colors */
  --color-danger: hsl(var(--danger-hue) 95% 45%);
  --color-danger-alpha: hsla(var(--danger-hue) 95% 45% / 0.6);
  --color-danger-lightest: hsl(var(--danger-hue) 75% 75%);
  --color-danger-light: hsl(var(--danger-hue) 85% 60%);
  --color-danger-light-alpha: hsla(var(--danger-hue) 85% 60% / 0.6);
  --color-danger-dark: hsl(var(--danger-hue) 100% 35%);
  --color-danger-dark-alpha: hsla(var(--danger-hue) 100% 35% / 0.6);
  --color-danger-darkest: hsl(var(--danger-hue) 100% 25%);
  --color-danger-muted: hsl(var(--danger-hue) 65% 45%);
  --color-danger-soft: hsl(var(--danger-hue) 45% 65%);

  /* Success Colors */
  --color-success: hsl(var(--success-hue) 95% 35%);
  --color-success-alpha: hsla(var(--success-hue) 95% 35% / 0.6);
  --color-success-lightest: hsl(var(--success-hue) 75% 65%);
  --color-success-light: hsl(var(--success-hue) 85% 50%);
  --color-success-light-alpha: hsla(var(--success-hue) 85% 50% / 0.6);
  --color-success-dark: hsl(var(--success-hue) 100% 25%);
  --color-success-dark-alpha: hsla(var(--success-hue) 100% 25% / 0.6);
  --color-success-darkest: hsl(var(--success-hue) 100% 15%);
  --color-success-muted: hsl(var(--success-hue) 65% 35%);
  --color-success-soft: hsl(var(--success-hue) 45% 55%);

  /* Info Colors */
  --color-info: hsl(var(--info-hue) 95% 45%);
  --color-info-alpha: hsla(var(--info-hue) 95% 45% / 0.6);
  --color-info-lightest: hsl(var(--info-hue) 75% 75%);
  --color-info-light: hsl(var(--info-hue) 85% 60%);
  --color-info-light-alpha: hsla(var(--info-hue) 85% 60% / 0.6);
  --color-info-dark: hsl(var(--info-hue) 100% 35%);
  --color-info-dark-alpha: hsla(var(--info-hue) 100% 35% / 0.6);
  --color-info-darkest: hsl(var(--info-hue) 100% 25%);
  --color-info-muted: hsl(var(--info-hue) 65% 45%);
  --color-info-soft: hsl(var(--info-hue) 45% 65%);

  /* Opacity Variants */
  --opacity-80: 0.8;
  --opacity-50: 0.5;
}

/* Opacity Classes */
.opacity-80 {
  opacity: var(--opacity-80);
}

.opacity-50 {
  opacity: var(--opacity-50);
}

/* Color Utility Classes */
.text-brand {
  color: var(--color-brand);
}
.bg-brand {
  background-color: var(--color-brand);
}
.border-brand {
  border-color: var(--color-brand);
}

/* Repeat for all color variants */
.text-primary {
  color: var(--color-primary);
}
.bg-primary {
  background-color: var(--color-primary);
}
.border-primary {
  border-color: var(--color-primary);
}

/* Add similar utility classes for all color variants */

/* After existing color definitions, add these utility classes: */

/* Text Colors */
.text-brand {
  color: var(--color-brand);
}
.text-brand-light {
  color: var(--color-brand-light);
}
.text-brand-dark {
  color: var(--color-brand-dark);
}
.text-brand-muted {
  color: var(--color-brand-muted);
}
.text-brand-soft {
  color: var(--color-brand-soft);
}

.text-primary {
  color: var(--color-primary);
}
.text-primary-light {
  color: var(--color-primary-light);
}
.text-primary-dark {
  color: var(--color-primary-dark);
}
.text-primary-muted {
  color: var(--color-primary-muted);
}
.text-primary-soft {
  color: var(--color-primary-soft);
}

.text-secondary {
  color: var(--color-secondary);
}
.text-secondary-light {
  color: var(--color-secondary-light);
}
.text-secondary-dark {
  color: var(--color-secondary-dark);
}
.text-secondary-muted {
  color: var(--color-secondary-muted);
}
.text-secondary-soft {
  color: var(--color-secondary-soft);
}

.text-accent {
  color: var(--color-accent);
}
.text-accent-light {
  color: var(--color-accent-light);
}
.text-accent-dark {
  color: var(--color-accent-dark);
}
.text-accent-muted {
  color: var(--color-accent-muted);
}
.text-accent-soft {
  color: var(--color-accent-soft);
}

/* Background Colors */
.bg-brand {
  background-color: var(--color-brand);
}
.bg-brand-light {
  background-color: var(--color-brand-light);
}
.bg-brand-dark {
  background-color: var(--color-brand-dark);
}
.bg-brand-muted {
  background-color: var(--color-brand-muted);
}
.bg-brand-soft {
  background-color: var(--color-brand-soft);
}

/* ... repeat for primary, secondary, accent */

/* Border Colors */
.border-brand {
  border-color: var(--color-brand);
}
.border-brand-light {
  border-color: var(--color-brand-light);
}
.border-brand-dark {
  border-color: var(--color-brand-dark);
}
.border-brand-muted {
  border-color: var(--color-brand-muted);
}
.border-brand-soft {
  border-color: var(--color-brand-soft);
}

/* ... repeat for primary, secondary, accent */

/* Status Colors */
.text-success {
  color: var(--color-success);
}
.bg-success {
  background-color: var(--color-success);
}
.border-success {
  border-color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}
.bg-warning {
  background-color: var(--color-warning);
}
.border-warning {
  border-color: var(--color-warning);
}

.text-danger {
  color: var(--color-danger);
}
.bg-danger {
  background-color: var(--color-danger);
}
.border-danger {
  border-color: var(--color-danger);
}

.text-info {
  color: var(--color-info);
}
.bg-info {
  background-color: var(--color-info);
}
.border-info {
  border-color: var(--color-info);
}

/* Opacity Variants */
.opacity-80 {
  opacity: var(--opacity-80);
}
.opacity-50 {
  opacity: var(--opacity-50);
}

/* Hover Effects */
.hover\:text-brand:hover {
  color: var(--color-brand);
}
.hover\:bg-brand:hover {
  background-color: var(--color-brand);
}
.hover\:border-brand:hover {
  border-color: var(--color-brand);
}

/* ... repeat hover variants for all colors */

/* Focus Effects */
.focus\:border-brand:focus {
  border-color: var(--color-brand);
}
.focus\:ring-brand:focus {
  box-shadow: 0 0 0 3px var(--color-brand-soft);
}

/* ... repeat focus variants for all colors */

/* Active States */
.active\:bg-brand:active {
  background-color: var(--color-brand-dark);
}
.active\:text-brand:active {
  color: var(--color-brand-dark);
}

/* ... repeat active variants for all colors */

/* Gradient Backgrounds */
.bg-gradient-brand {
  background-image: linear-gradient(
    to bottom right,
    var(--color-brand-light),
    var(--color-brand-dark)
  );
}

/* ... repeat gradients for all colors */

/* Border Styles */
.border-1 {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border {
  border-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}

/* Rounded Corners */
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-full {
  border-radius: 9999px;
}
