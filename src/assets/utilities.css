:root {
  --box-shadow-light: 0 0.5em 8px -0.5em #0005, 0 0.3em 3px -0.2em #0003;
  --box-shadow-dark: 0 0.5em 3px -0.3em #0008;

  --border-color: light-dark(#aaa, transparent);
}
/* Button Link Utilities */
a.button-link {
  --button-color: var(--color-primary);
  --button-color-dark: var(--color-primary-dark);
  --button-text: var(--fg);
  display: inline-flex;
  align-items: center;
  gap: var(--space-gap-xs);
  cursor: pointer;
  transition: all 0.2s ease;
  border: calc(var(--unit) * 0.25) solid transparent;
  padding: 0.5em 1em;
  text-decoration: none;
  font-size: var(--step-0);
  font-weight: 500;
  background-image: linear-gradient(to bottom right, #fff1, #0001);
  box-shadow:
    -0.06em -0.06em 0.09em rgba(0, 0, 0, 0.25) inset,
    0.06em 0.06em 0.09em rgba(255, 255, 255, 0.5) inset;
}

.shadow {
  box-shadow: var(--box-shadow-light);
  @media (prefers-color-scheme = dark) {
    box-shadow: var(--box-shadow-dark);
  }
}

/* Button Link Variants */
a.button-link.button-link--solid {
  background-color: var(--button-color);
  border-color: var(--button-color-dark);
  color: var(--button-text);
}

a.button-link.button-link--outline {
  background-color: transparent;
  border-color: var(--button-color);
  color: var(--button-text);
}

a.button-link.button-link--ghost {
  background-color: transparent;
  border-color: transparent;
  color: var(--button-text);
  box-shadow: none;
}

/* Button Link Sizes */
a.button-link.button-link--tiny {
  font-size: var(--step--2);
  gap: var(--space-gap-xxs);
  padding: 0.25em 0.5em;
}

a.button-link.button-link--compact {
  font-size: var(--step--1);
  padding: 0.33em 0.75em;
}

a.button-link.button-link--large {
  font-size: var(--step-1);
  padding: 0.75em 1.5em;
}

/* Button Link Shapes */
a.button-link.button-link--rounded {
  border-radius: var(--radius-sm);
}

a.button-link.button-link--pill {
  border-radius: 100vw;
}

/* Button Link States */
a.button-link:hover:not(:disabled) {
  filter: brightness(1.25);
  background-color: var(--button-color);
  text-decoration: none;
  color: var(--button-text);
}

a.button-link:active:not(:disabled) {
  filter: brightness(0.95);
  transform: translateY(1px);
  box-shadow:
    0.03em 0.03em 0.06em rgba(0, 0, 0, 0.25) inset,
    -0.03em -0.03em 0.06em rgba(255, 255, 255, 0.5) inset;
}

a.button-link:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Button Link Purposes */
a.button-link.button-link--primary {
  --button-color: var(--color-primary);
  --button-color-dark: var(--color-primary-dark);
  --button-text: var(--white);
}

a.button-link.button-link--secondary {
  --button-color: var(--color-secondary);
  --button-color-dark: var(--color-secondary-dark);
  --button-text: var(--color-white);
}

a.button-link.button-link--accent {
  --button-color: var(--color-accent);
  --button-color-dark: var(--color-accent-dark);
  --button-text: var(--color-white);
}

a.button-link.button-link--success {
  --button-color: var(--color-success);
  --button-color-dark: var(--color-success-dark);
  --button-text: var(--color-white);
}

a.button-link.button-link--danger {
  --button-color: var(--color-danger);
  --button-color-dark: var(--color-danger-dark);
  --button-text: var(--color-white);
}

a.button-link.button-link--warning {
  --button-color: var(--color-warning);
  --button-color-dark: var(--color-warning-dark);
  --button-text: var(--color-black);
}

a.button-link.button-link--info {
  --button-color: var(--color-info);
  --button-color-dark: var(--color-info-dark);
  --button-text: var(--color-white);
}

/* Apply color variables */
a.button-link.button-link--solid {
  background-color: var(--button-color);
  border-color: var(--button-color-dark);
  color: var(--button-text);
}

a.button-link.button-link--outline {
  border-color: var(--button-color);
  color: var(--button-color);
}

a.button-link.button-link--outline:hover:not(:disabled) {
  color: var(--button-text);
}

a.button-link.button-link--ghost {
  color: var(--button-color);
}

a.button-link.button-link--ghost:hover:not(:disabled) {
  color: var(--button-text);
}
