<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { doc, updateDoc } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'
import { vAutoAnimate } from '@formkit/auto-animate/vue'
import { useDebounceFn } from '@vueuse/core'
import { useFirebase } from '@/composables/useFirebase'
import { useUsers } from '@/composables/useUsers.ts'
import { User } from '@/models/User.ts'
import type { RoleType } from '@/types/models'
import type { UserRole } from '@/types/user'
import type { DocumentReference } from 'firebase/firestore'
import UserCard from '@/components/user/UserCard.vue'

const { db } = useFirebase()
const auth = getAuth()

const success = ref<string | null>(null)

const {
  sortedUsers,
  isLoading,
  error,
  subscribeToUsers,
  cleanup: cleanupUsers
} = useUsers()

const localError = ref<string | null>(null)
const editMode = ref<boolean>(false)

const editedUser = ref<Partial<User>>({
  id: '',
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  roles: []
})

const selectedRole = ref<RoleType | null>(null)

const availableRoles: { value: RoleType; label: string }[] = [
  { value: 'admin', label: 'Administrator' },
  { value: 'artist', label: 'Artist' },
  { value: 'bandLeader', label: 'Band Leader' },
  { value: 'user', label: 'User' }
]

// Debounced update function
const debouncedUpdateUser = useDebounceFn(async (userData: Partial<User>) => {
  try {
    if (!userData.id) {
      console.error('Cannot update user: missing id')
      return
    }

    const userRef: DocumentReference = doc(db, 'users', userData.id)
    await updateDoc(userRef, {
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      phone: userData.phone
    })
  } catch (error) {
    console.error('Error updating user:', error)
  }
}, 500)

function cancelEditing(): void {
  editMode.value = false
  editedUser.value = {
    id: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    roles: []
  }
  localError.value = null
  success.value = null
}

async function saveUser(): Promise<void> {
  if (!editedUser.value.id) return

  try {
    isLoading.value = true
    localError.value = null
    success.value = null

    const userRef = doc(db, 'users', editedUser.value.id)
    await updateDoc(userRef, {
      firstName: editedUser.value.firstName,
      lastName: editedUser.value.lastName,
      phone: editedUser.value.phone,
      roles: editedUser.value.roles
    })

    editMode.value = false
  } catch (err: unknown) {
    console.error('Error updating user:', err)
    localError.value = `Failed to update user: ${err instanceof Error ? err.message : 'Unknown error'}`
  } finally {
    isLoading.value = false
  }
}

function addRole(role: RoleType) {
  if (!editedUser.value.roles) {
    editedUser.value.roles = []
  }

  // Check if role already exists
  const existingRole = editedUser.value.roles.find(r => r.type === role)
  if (existingRole) {
    return // Don't add duplicate roles
  }

  const roleObj: UserRole = { type: role }
  editedUser.value.roles.push(roleObj)
  selectedRole.value = null
}

function removeRole(roleName: RoleType) {
  if (editedUser.value.roles) {
    editedUser.value.roles = editedUser.value.roles.filter(
      role => role.type !== roleName
    )
  }
}

function formatRoleName(roleName: RoleType | null): string {
  switch (roleName?.toLowerCase()) {
    case 'artist':
      return 'Artist'
    case 'bandleader':
      return 'Band Leader'
    case 'admin':
      return 'Administrator'
    case 'user':
      return 'User'
    default:
      return roleName ? roleName.charAt(0).toUpperCase() + roleName.slice(1) : ''
  }
}

// Safe handler for adding roles
function handleAddRole(): void {
  if (selectedRole.value) {
    addRole(selectedRole.value)
  }
}

onMounted(() => {
  subscribeToUsers()
})

onUnmounted(() => {
  cleanupUsers()
})
</script>

<template>
  <BaseSection title="Users">
    <div v-if="localError" class="error-message" role="alert">
      {{ localError }}
    </div>

    <div v-if="error" class="error-message" role="alert">
      {{ error }}
    </div>

    <div v-if="success" class="success-message" role="alert">
      {{ success }}
    </div>

    <div v-if="isLoading" class="loading">
      Loading users...
    </div>

    <div v-else v-auto-animate class="user-list">
      <div v-if="sortedUsers.length === 0" class="no-users">
        No users found.
      </div>
      <UserCard v-else v-for="user in sortedUsers" :key="user.id" class="user-item" :user />
    </div>
  </BaseSection>
</template>

<style scoped>
.user-list {
  display: grid;
  gap: var(--space-s);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.loading {
  text-align: center;
  padding: var(--space-m);
  color: var(--color-text-muted);
}

.error-message {
  color: var(--color-danger);
  margin-bottom: var(--space-m);
}

.success-message {
  color: var(--color-success);
  margin-bottom: var(--space-m);
}
</style>
