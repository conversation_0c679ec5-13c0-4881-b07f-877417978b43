<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUsers } from '@/composables/useUsers'
import { useAuth } from '@/composables/useAuth'
import { User } from '@/models/User'

interface NameComparison {
  auth: {
    displayName: string
    firstName: string
    lastName: string
  }
  firestore: {
    firstName: string
    lastName: string
  }
  matches: {
    firstName: boolean
    lastName: boolean
  }
}

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const { getUser, updateUserProfile, isCurrentUser } = useUsers()
const { user: authUser, isAdmin } = useAuth()

const user = ref<User | null>(null)
const nameComparison = ref<NameComparison | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const selectedNameSource = ref<'auth' | 'firestore' | null>(null)
const selectedNames = ref({
  firstName: '',
  lastName: ''
})

const canEditNames = computed(() => {
  if (!user.value) return false
  return isCurrentUser(user.value) || isAdmin.value
})

// Watch for changes in the selected name source
watch(selectedNameSource, (newSource) => {
  if (!nameComparison.value) return

  if (newSource === 'auth') {
    selectedNames.value = {
      firstName: nameComparison.value.auth.firstName,
      lastName: nameComparison.value.auth.lastName
    }
  } else if (newSource === 'firestore') {
    selectedNames.value = {
      firstName: nameComparison.value.firestore.firstName,
      lastName: nameComparison.value.firestore.lastName
    }
  }
})

onMounted(async () => {
  try {
    const userData = await getUser(props.id)
    if (!userData) {
      throw new Error('User not found')
    }
    user.value = new User(userData)

    // Initialize selected names with Firestore values
    selectedNames.value = {
      firstName: userData.firstName || '',
      lastName: userData.lastName || ''
    }

    // If it's current user, check for name mismatch
    if (authUser.value?.uid === props.id && authUser.value.displayName) {
      const nameParts = authUser.value.displayName.split(' ')
      const authFirstName = nameParts[0] || ''
      const authLastName = nameParts.slice(1).join(' ') || ''
      nameComparison.value = {
        auth: {
          displayName: authUser.value.displayName,
          firstName: authFirstName,
          lastName: authLastName
        },
        firestore: {
          firstName: userData.firstName,
          lastName: userData.lastName
        },
        matches: {
          firstName: authFirstName === userData.firstName,
          lastName: authLastName === userData.lastName
        }
      }
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
})

async function saveUser() {
  if (!user.value || !canEditNames.value) return
  try {
    isLoading.value = true
    error.value = null

    // Update user profile (this should handle both Firestore and Auth if it's the current user)
    const success = await updateUserProfile({
      id: props.id,
      firstName: selectedNames.value.firstName,
      lastName: selectedNames.value.lastName
    })

    if (!success) throw new Error('Failed to update user profile')

    router.push('/users')
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
}

function cancelEdit() {
  router.push('/users')
}
</script>

<template>
  <BaseSection title="Edit User">
    <template v-if="isLoading">
      <BaseCard>Loading user details...</BaseCard>
    </template>

    <template v-else-if="error">
      <BaseCard>{{ error }}</BaseCard>
    </template>

    <template v-else-if="user">
      <BaseCard>
        <!-- Name Mismatch Warning -->
        <div v-if="nameComparison && (!nameComparison.matches.firstName || !nameComparison.matches.lastName)"
          class="name-mismatch">
          <div class="name-mismatch-header">
            <h3>Name Mismatch Detected</h3>
          </div>

          <div v-if="canEditNames" class="name-options">
            <fieldset>
              <div class="name-options-grid">
                <h4>Which name is correct?</h4>
                <BaseButton purpose="secondary"
                  :class="{ 'name-option-button-selected': selectedNameSource === 'auth' }"
                  @click="selectedNameSource = 'auth'">
                  Use {{ nameComparison.auth.firstName }} {{ nameComparison.auth.lastName }}
                </BaseButton>
                <BaseButton purpose="secondary"
                  :class="{ 'name-option-button-selected': selectedNameSource === 'firestore' }"
                  @click="selectedNameSource = 'firestore'">
                  Use {{ nameComparison.firestore.firstName }} {{ nameComparison.firestore.lastName }}
                </BaseButton>
              </div>
            </fieldset>

            <div v-if="selectedNameSource" class="name-preview">
              <div class="name-preview-header">
                <h4>Names that will be saved:</h4>
              </div>
              <div class="name-preview-details">
                <div class="name-field">
                  <span class="name-label">First Name:</span>
                  <strong>{{ selectedNames.firstName }}</strong>
                </div>
                <div class="name-field">
                  <span class="name-label">Last Name:</span>
                  <strong>{{ selectedNames.lastName }}</strong>
                </div>
              </div>
              <p class="name-preview-note">
                These names will be synchronized in both the authentication system and database.
              </p>
            </div>
          </div>

          <div v-else class="name-mismatch-readonly">
            <p>Only administrators or the account owner can resolve name mismatches.</p>
          </div>
        </div>

        <form @submit.prevent="saveUser" class="edit-form">
          <!-- User Form -->
          <div class="form-group">
            <label for="firstName">First Name</label>
            <input id="firstName" v-model="selectedNames.firstName" type="text" required :disabled="!canEditNames">
          </div>

          <div class="form-group">
            <label for="lastName">Last Name</label>
            <input id="lastName" v-model="selectedNames.lastName" type="text" required :disabled="!canEditNames">
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input id="email" v-model="user.email" type="email" class="form-control" disabled>
          </div>

          <div class="form-actions">
            <BaseButton type="submit" :disabled="!canEditNames">Save Changes</BaseButton>
            <BaseButton type="button" @click="cancelEdit" variant="outline">Cancel</BaseButton>
          </div>
        </form>
      </BaseCard>
    </template>
  </BaseSection>
</template>

<style scoped>
.edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  margin-top: var(--space-m);
}

.name-mismatch {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  margin-bottom: var(--space-xl);
}

.name-mismatch-header {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.name-mismatch-header h3 {
  color: var(--color-warning);
  font-size: var(--step-1);
  font-weight: 600;
  margin: 0;
}

.name-mismatch-explanation {
  color: var(--color-text-muted);
  font-size: var(--step-0);
  line-height: 1.5;
  margin: 0;
}

.name-options {
  display: grid;
  gap: var(--space-s);
}

.name-options fieldset {
  border: 1px solid var(--color-warning);
  background-color: var(--color-brand-dark);
  border-radius: var(--radius-l);
  padding: var(--space-s);
}

.name-options legend {
  padding: 0 var(--space-s);
  font-weight: 500;
  color: var(--color-warning);
  font-size: var(--step-0);
}

.name-options-grid {
  display: grid;
  gap: var(--space-s);
  grid-template-columns: auto 1fr 1fr;
}

.name-option {
  display: flex;
  gap: var(--space-s);
  padding: var(--space-m);
  border: 2px solid var(--color-border-subtle);
  border-radius: var(--radius-l);
  background: var(--color-background);
  transition: all 0.2s ease-in-out;
}

.name-option:hover {
  border-color: var(--color-border-primary);
  background: var(--color-bg-subtle);
}

.name-option-radio {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding-top: var(--space-xs);
}

.name-option-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.radio-circle {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border-muted);
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.name-option-radio input[type="radio"]:checked+.radio-circle {
  border-color: var(--color-border-success);
  background: var(--color-bg-success);
  box-shadow: inset 0 0 0 4px var(--color-bg-default);
}

.name-option label {
  flex: 1;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.name-option-header h4 {
  margin: 0;
  font-size: var(--step-0);
  font-weight: 500;
  color: var(--color-text-default);
}

.name-option-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.name-field {
  display: flex;
  gap: var(--space-xs);
  align-items: baseline;
  font-size: var(--step--1);
}

.name-label {
  color: var(--color-text-muted);
  min-width: 80px;
}

.name-preview {
  background: var(--color-success-dark);
  border: 1px solid var(--color-success);
  border-radius: var(--radius-l);
  padding: var(--space-l);
}

.name-preview-header h4 {
  margin: 0 0 var(--space-m);
  font-size: var(--step-0);
  font-weight: 500;
}

.name-preview-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  margin-bottom: var(--space-m);
}

.name-preview-note {
  font-size: var(--step--1);
  margin: 0;
}

.name-mismatch-readonly {
  color: var(--color-text-muted);
  font-style: italic;
  padding: var(--space-m);
  background: var(--color-surface);
  border-radius: var(--radius-m);
}

.name-option-button-selected {
  border-color: var(--color-success);
  background: var(--color-success-dark);
  color: var(--color-text);
}
</style>
