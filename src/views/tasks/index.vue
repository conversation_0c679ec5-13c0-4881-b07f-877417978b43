<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useTasks } from '@/composables/useTasks'
import { useUsers } from '@/composables/useUsers'
import { useFirebase } from '@/composables/useFirebase'
import { Task } from '@/models/Task'
import TaskCard from '@/components/tasks/TaskCard.vue'
import { Timestamp } from 'firebase/firestore'
import { Plus } from 'lucide-vue-next'
import { useTitle } from '@vueuse/core'
import { useRoute } from 'vue-router'

type TaskFormData = {
  title: string
  details?: string
  isUrgent: boolean
  isCompleted: boolean
  dueDate: Date | null
  assignedTo: string | null
  author: string
  authorEmail: string
}

// Form state separate from Task model
type TaskFormState = {
  id: string
  title: string
  details?: string
  isUrgent: boolean
  isCompleted: boolean
  dueDate: string // Keep as string for input[type="date"]
  assignedTo: string | null
}

const {
  tasks: myTasks,
  loading,
  error,
  subscribeToTasks,
  createTask,
  updateTask,
  toggleTaskCompletion,
  toggleTaskUrgency,
  deleteTask,
  cleanup: cleanupTasks,
} = useTasks()

const { users, subscribeToUsers, cleanup: cleanupUsers } = useUsers()
const { currentUser } = useFirebase()

// Computed property for assignable users
const assignableUsers = computed(() =>
  users.value.filter(user =>
    user.hasRole('admin') ||
    user.hasRole('artist') ||
    user.hasRole('musician')
  )
)

// New task form
const showNewTaskForm = ref(false)
const isEditMode = ref(false)
const showDueDate = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const defaultFormState: TaskFormState = {
  id: '',
  title: '',
  details: '',
  isUrgent: false,
  isCompleted: false,
  dueDate: '',
  assignedTo: null
}

const taskForm = ref<TaskFormState>({ ...defaultFormState })

// Form validation
const isFormValid = computed(() => {
  // Check required fields
  if (!taskForm.value.title || !taskForm.value.title.trim()) return false
  // Check date validity if a due date is set
  if (showDueDate.value && !isDateValid.value) return false
  return true
})

// Date validation
const isDateValid = computed(() => {
  if (!taskForm.value.dueDate) return true
  const selectedDate = new Date(taskForm.value.dueDate + 'T23:59:59')
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return selectedDate >= today
})

const dateErrorMessage = computed(() => {
  if (!taskForm.value.dueDate || isDateValid.value) return ''
  return 'Due date cannot be in the past'
})

// Computed property for minimum date (today)
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0] // Format to YYYY-MM-DD
})

const userOptions = computed(() => {
  return assignableUsers.value.map(user => ({
    value: user.email,
    label: user.displayName || user.email
  }))
})

function resetForm() {
  taskForm.value = { ...defaultFormState }
  showDueDate.value = false
  isEditMode.value = false
  successMessage.value = ''
  errorMessage.value = ''
}

function createTaskData(): TaskFormData {
  if (!currentUser.value) throw new Error('No user logged in')

  return {
    title: taskForm.value.title.trim(),
    details: taskForm.value.details?.trim() ?? '',
    isUrgent: taskForm.value.isUrgent,
    isCompleted: false,
    dueDate: showDueDate.value && taskForm.value.dueDate ? new Date(taskForm.value.dueDate + 'T23:59:59') : null,
    assignedTo: taskForm.value.assignedTo,
    author: currentUser.value.displayName || 'Unknown',
    authorEmail: currentUser.value.email || 'Unknown'
  }
}

async function submitNewTask() {
  if (!currentUser.value) return

  if (!isFormValid.value) {
    errorMessage.value = 'Please fix the errors in the form before submitting'
    return
  }

  try {
    if (isEditMode.value && taskForm.value.id) {
      await updateTask(taskForm.value.id, {
        title: taskForm.value.title.trim(),
        details: taskForm.value.details?.trim() ?? '',
        isUrgent: taskForm.value.isUrgent,
        isCompleted: taskForm.value.isCompleted,
        dueDate: showDueDate.value && taskForm.value.dueDate ? new Date(taskForm.value.dueDate + 'T23:59:59') : null,
        assignedTo: taskForm.value.assignedTo
      })
    } else {
      await createTask(createTaskData())
    }
    resetForm()
    showNewTaskForm.value = false
  } catch (error) {
    console.error('Error saving task:', error)
    errorMessage.value = 'Failed to save task. Please try again.'
  }
}

// Watch showDueDate to set tomorrow when checked if no date is set
watch(showDueDate, (newValue) => {
  if (newValue && !taskForm.value.dueDate) {
    // Set to tomorrow if no date is currently set
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    taskForm.value.dueDate = tomorrow.toISOString().split('T')[0]
  }
})

function handleEditTask(task: Task) {
  // Don't allow editing tasks with past due dates to have due dates in the past
  const dueDate = task.dueDate
    ? task.dueDate < new Date()
      ? ''
      : task.dueDate.toISOString().split('T')[0]
    : ''

  // Reset form first to trigger TinyMCE reinit
  resetForm()

  // Then set the form values
  taskForm.value = {
    id: task.id,
    title: task.title,
    details: task.details || '',
    isUrgent: task.isUrgent,
    isCompleted: task.isCompleted,
    dueDate,
    assignedTo: task.assignedTo
  }

  // Show due date checkbox if there is a due date and it's in the future or today
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  showDueDate.value = !!task.dueDate && task.dueDate.getTime() >= today.getTime()
  isEditMode.value = true
  showNewTaskForm.value = true
}

// Task actions
function handleToggleCompletion(taskId: string, isCompleted: boolean) {
  toggleTaskCompletion(taskId, isCompleted)
}

function handleToggleUrgency(taskId: string, isUrgent: boolean) {
  toggleTaskUrgency(taskId, isUrgent)
}

function handleDeleteTask(taskId: string) {
  deleteTask(taskId)
}

const route = useRoute()

// Lifecycle
onMounted(() => {
  subscribeToTasks()
  subscribeToUsers()

  if (route.name === 'tasks.create') {
    resetForm()
    showNewTaskForm.value = true
  }

  watch([urgentTasks, otherTasks], () => {
    if (urgentTasks.value.length + otherTasks.value.length === 0) {
      useTitle('Tasks')
      return
    }
    useTitle(`Tasks (${urgentTasks.value.length + otherTasks.value.length})`)
  })
})

onUnmounted(() => {
  cleanupTasks()
  cleanupUsers()
})

// Tasks by status
const completedTasks = computed(() =>
  myTasks.value
    .filter(task => task.isCompleted)
    .sort((a, b) => {
      const dateA = a.completedAt instanceof Date ? a.completedAt.getTime() : 0
      const dateB = b.completedAt instanceof Date ? b.completedAt.getTime() : 0
      return dateB - dateA
    })
    .slice(0, 3)
    .map(task => new Task({
      id: task.id,
      title: task.title,
      details: task.details,
      isUrgent: task.isUrgent,
      isCompleted: task.isCompleted,
      author: task.author,
      authorEmail: task.authorEmail,
      assignedTo: task.assignedTo,
      dueDate: task.dueDate ? Timestamp.fromDate(task.dueDate) : undefined,
      completedBy: task.completedBy,
      createdAt: Timestamp.fromDate(task.createdAt),
      updatedAt: Timestamp.fromDate(task.updatedAt),
      completedAt: task.completedAt ? Timestamp.fromDate(task.completedAt) : null
    }))
)

const urgentTasks = computed(() =>
  myTasks.value
    .filter(task => !task.isCompleted && task.isUrgent)
    .sort((a, b) => {
      const dateA = a.dueDate instanceof Date ? a.dueDate.getTime() : 0
      const dateB = b.dueDate instanceof Date ? b.dueDate.getTime() : 0
      return dateA - dateB
    })
    .map(task => new Task({
      id: task.id,
      title: task.title,
      details: task.details,
      isUrgent: task.isUrgent,
      isCompleted: task.isCompleted,
      author: task.author,
      authorEmail: task.authorEmail,
      assignedTo: task.assignedTo,
      dueDate: task.dueDate ? Timestamp.fromDate(task.dueDate) : undefined,
      completedBy: task.completedBy,
      createdAt: Timestamp.fromDate(task.createdAt),
      updatedAt: Timestamp.fromDate(task.updatedAt),
      completedAt: task.completedAt ? Timestamp.fromDate(task.completedAt) : null
    }))
)

const otherTasks = computed(() =>
  myTasks.value
    .filter(task => !task.isCompleted && !task.isUrgent)
    .sort((a, b) => {
      const dateA = a.createdAt instanceof Date ? a.createdAt.getTime() : 0
      const dateB = b.createdAt instanceof Date ? b.createdAt.getTime() : 0
      return dateB - dateA
    })
    .map(task => new Task({
      id: task.id,
      title: task.title,
      details: task.details,
      isUrgent: task.isUrgent,
      isCompleted: task.isCompleted,
      author: task.author,
      authorEmail: task.authorEmail,
      assignedTo: task.assignedTo,
      dueDate: task.dueDate ? Timestamp.fromDate(task.dueDate) : undefined,
      completedBy: task.completedBy,
      createdAt: Timestamp.fromDate(task.createdAt),
      updatedAt: Timestamp.fromDate(task.updatedAt),
      completedAt: task.completedAt ? Timestamp.fromDate(task.completedAt) : null
    }))
)
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="tasks-view__header">
        <h1>Tasks</h1>
        <BaseButton v-if="!showNewTaskForm" purpose="primary" @click="() => {
          resetForm()
          showNewTaskForm = true
        }">
          <Plus class="icon" />
          New Task
        </BaseButton>
      </div>
    </template>

    <div v-if="showNewTaskForm" class="tasks-view__form">
      <form @submit.prevent="submitNewTask" class="form">
        <h2>{{ isEditMode ? 'Edit Task' : 'New Task' }}</h2>
        <div class="form__group">
          <label for="title">Title *</label>
          <BaseInput id="title" v-model="taskForm.title" placeholder="Enter task title" required />
        </div>

        <div class="form__group">
          <label for="details">Details</label>
          <BaseEditor id="details" v-model="taskForm.details" placeholder="Enter task details" />
        </div>

        <div class="form__group checkbox">
          <input type="checkbox" id="showDueDate" v-model="showDueDate" />
          <label for="showDueDate">Add due date</label>
        </div>

        <div v-if="showDueDate" class="form__group">
          <label for="dueDate">Due Date</label>
          <BaseInput id="dueDate" v-model="taskForm.dueDate" type="date" :min="minDate"
            :class="{ 'input--error': !isDateValid }" placeholder="Select due date" />
          <small :class="{
            'form__help': isDateValid,
            'form__error': !isDateValid
          }">
            {{ dateErrorMessage }}
          </small>
        </div>

        <div class="form__group">
          <label for="assignedTo">Assign To</label>
          <BaseSelect v-model="taskForm.assignedTo" :options="userOptions" id="task-form-assigned-to-select" />
        </div>

        <div class="form__group checkbox">
          <input type="checkbox" id="isUrgent" v-model="taskForm.isUrgent" />
          <label for="isUrgent">Mark as urgent</label>
        </div>

        <div class="form__actions">
          <BaseButton purpose="secondary" @click="showNewTaskForm = false">
            Cancel
          </BaseButton>
          <BaseButton type="submit" purpose="primary" :disabled="!isFormValid">
            {{ isEditMode ? 'Save Changes' : 'Create Task' }}
          </BaseButton>
        </div>
        <div v-if="errorMessage" class="form__error form__error--submit">
          {{ errorMessage }}
        </div>
      </form>
    </div>

    <div v-if="loading" class="tasks-view__loading">
      Loading tasks...
    </div>

    <div v-else-if="error" class="tasks-view__error">
      {{ error }}
    </div>

    <div v-else class="tasks-view__content">
      <div v-if="urgentTasks.length > 0" class="tasks-view__section">
        <h2>Urgent Tasks ({{ urgentTasks.length }})</h2>
        <div class="tasks-view__list">
          <TaskCard v-for="task in urgentTasks" :key="task.id" :task="task" @toggle-completion="handleToggleCompletion"
            @toggle-urgency="handleToggleUrgency" @delete="handleDeleteTask" @edit="handleEditTask" />
        </div>
      </div>

      <div v-if="otherTasks.length > 0" class="tasks-view__section">
        <h2>{{ urgentTasks.length > 0 ? 'Other Tasks' : 'Upcoming Tasks' }} ({{ otherTasks.length }})</h2>
        <div class="tasks-view__list">
          <TaskCard v-for="task in otherTasks" :key="task.id" :task="task" @toggle-completion="handleToggleCompletion"
            @toggle-urgency="handleToggleUrgency" @delete="handleDeleteTask" @edit="handleEditTask" />
        </div>
      </div>

      <div v-if="completedTasks.length > 0" class="tasks-view__section">
        <h2>Recently Completed Tasks</h2>
        <div class="tasks-view__list">
          <TaskCard v-for="task in completedTasks" :key="task.id" :task="task"
            @toggle-completion="handleToggleCompletion" @toggle-urgency="handleToggleUrgency" @delete="handleDeleteTask"
            @edit="handleEditTask" />
        </div>
      </div>

      <div v-if="!urgentTasks.length && !otherTasks.length && !completedTasks.length" class="tasks-view__empty">
        <p>No tasks today.</p>
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.tasks-view__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-m);

  h1 {
    font-size: var(--step-2);
  }
}

.tasks-view__section h2 {
  font-size: var(--step-1);
  margin-block: var(--space-m) var(--space-2xs);
  opacity: .8;
}

.tasks-view__form {
  padding: var(--space-m);
  background-color: var(--color-background-soft);
  border-radius: var(--radius);
}

.tasks-view__form h2 {
  font-size: var(--step-1);
  color: var(--color-heading);
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  margin-inline: auto;
  min-width: 500px;
}

.form__group {
  display: flex;
  flex-direction: column;
  gap: var(--space-3xs);
}

.form__group label {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  font-weight: 500;
}

.form__group.checkbox {
  flex-direction: row;
  align-items: center;
  gap: var(--space-2xs);
}

.form__group.checkbox input[type="checkbox"] {
  width: var(--step-0);
  height: var(--step-0);
}

.form__group.checkbox label {
  cursor: pointer;
}

.form__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-s);
  margin-top: var(--space-m);
  padding-top: var(--space-m);
  border-top: 1px solid var(--color-border);
}

.tasks-view__list {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.tasks-view__empty {
  text-align: center;
  color: var(--color-text-muted);
  padding: var(--space-xl) 0;
  font-size: var(--step-0);
}

.tasks-view__loading,
.tasks-view__error {
  text-align: center;
  padding: var(--space-xl) 0;
  font-size: var(--step-0);
}

.tasks-view__error {
  color: var(--color-danger);
}

.form__help {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  margin-top: var(--space-3xs);
}

.form__error {
  font-size: var(--step--1);
  color: var(--color-danger);
  margin-top: var(--space-3xs);
}

.input--error {
  border-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.input--error:focus {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 2px var(--color-danger-light);
}

.form__error--submit {
  margin-top: var(--space-s);
  text-align: center;
  padding: var(--space-xs);
  background-color: var(--color-danger-light);
  border-radius: var(--radius);
}
</style>
