<script setup lang="ts">
import { ref } from 'vue'
import AdminCard from '@/components/AdminCard.vue'
import type { Section } from '@/components/AdminCard.vue'

const sections = ref<Section[]>([
  {
    id: 'acts',
    title: 'Acts',
    description: 'Manage performers, bands, and entertainers',
    icon: '⬚',
    route: '/acts',
    stats: {
      total: '23',
      active: '18'
    }
  },
  {
    id: 'events',
    title: 'Events',
    description: 'Schedule and manage upcoming shows',
    icon: '⬚',
    route: '/events',
    stats: {
      upcoming: '12',
      past: '45'
    }
  },
  {
    id: 'venues',
    title: 'Venues',
    description: 'Manage performance locations',
    icon: '⬚',
    route: '/venues',
    stats: {
      total: '8',
      active: '6'
    }
  },
  {
    id: 'shows',
    title: 'Shows',
    description: 'Configure show types and templates',
    icon: '⬚',
    route: '/shows',
    stats: {
      types: '4',
      templates: '7'
    }
  }
])
</script>

<template>
  <header class="dashboard-header">
    <h1>Admin Dashboard</h1>
    <p class="dashboard-subtitle">Welcome back! Here's your overview.</p>
  </header>

  <div class="dashboard-grid">
    <AdminCard v-for="section in sections" :key="section.title" :section="section" />
  </div>

  <section class="dashboard-section">
    <h2>Quick Insights</h2>
    <div class="insights-grid">
      <div class="insight-card">
        <h3>Upcoming Events</h3>
        <!-- Add chart or list component here -->
      </div>
      <div class="insight-card">
        <h3>Popular Venues</h3>
        <!-- Add chart or list component here -->
      </div>
      <div class="insight-card">
        <h3>Active Acts</h3>
        <!-- Add chart or list component here -->
      </div>
    </div>
  </section>
</template>

<style scoped>
.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-subtitle {
  color: var(--color-text-muted);
  font-size: var(--step-1);
}

.dashboard-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-template-rows: auto auto 1fr auto;
  margin-bottom: 3rem;
}

.dashboard-section {
  margin-top: 3rem;
}

.insights-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  margin-top: 1rem;
}

.insight-card {
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  min-height: 300px;
}

@media (max-width: 768px) {
  .dashboard-card__actions {
    flex-direction: column;
  }
}
</style>
