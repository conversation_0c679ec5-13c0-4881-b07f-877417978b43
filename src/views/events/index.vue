<script setup lang="ts">
import { useEvents } from '@/composables/useEvents'
import { Search, X, ArrowUpDown, Plus } from 'lucide-vue-next'
import DateRangeSelector from '../../components/DateRangeSelector.vue'
import EventCard from '../../components/EventCard.vue'
import { ref, onUnmounted, watch, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Event } from '@/models/Event'

const router = useRouter()

// Initialize data with default values
const fromDate = ref(new Date())
const selectedFromDate = ref<Date | null>(null)
const selectedToDate = ref<Date | null>(null)
const selectedYear = ref<string>('')
const sortDescending = ref(false)
const searchQuery = ref<string>('')
const selectedActs = ref<string[]>([])
const showFilters = ref(false)

// Initialize useEvents with default filters
const {
  events,
  isLoading: isLoadingEvents,
  areDetailsLoading,
  error,
  subscribeToEvents,
  cleanup
} = useEvents({
  sortDescending: false
})

// Computed property to get all available years from raw events data
const availableYears = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  const years: Set<number> = new Set()
  events.value.forEach(event => {
    if (event?.when instanceof Date) {
      years.add(event.when.getFullYear())
    }
  })
  return Array.from(years).sort((a, b) => b - a)
})

// Get unique acts from all events
const availableActs = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  const actsMap = new Map()
  events.value.forEach((event) => {
    if (event?.actDetails) {
      event.actDetails.forEach(act => {
        if (act?.id && !actsMap.has(act.id)) {
          actsMap.set(act.id, act)
        }
      })
    }
  })

  return Array.from(actsMap.values())
})

// Filter events based on search and acts
const filteredEvents = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  let filtered = events.value

  // Filter by selected acts
  if (selectedActs.value.length > 0) {
    filtered = filtered.filter((event) => {
      return event?.acts && selectedActs.value.some(actId => event.acts.includes(actId))
    })
  }

  // Filter by search query
  if (searchQuery.value) {
    filtered = filtered.filter((event) => {
      return event?.matchesVenueSearch && event.matchesVenueSearch(searchQuery.value)
    })
  }

  return filtered
})

// Initialize data
async function initializeEvents() {
  try {
    await subscribeToEvents({
      fromDate: selectedFromDate.value ?? fromDate.value,
      toDate: selectedToDate.value ?? undefined,
      sortDescending: sortDescending.value
    })
  } catch (err) {
    console.error('Failed to initialize events:', err)
  }
}

onMounted(async () => {
  await initializeEvents()
})

// Watch for filter changes
watch([selectedFromDate, selectedToDate, sortDescending], async () => {
  await initializeEvents()
})

// Cleanup subscription on component unmount
onUnmounted(() => {
  cleanup()
})

// Toggle act selection
function toggleAct(actId: string) {
  const index = selectedActs.value.indexOf(actId)
  if (index === -1) {
    selectedActs.value.push(actId)
  } else {
    selectedActs.value.splice(index, 1)
  }
}
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="section-header">
        <div class="title-group">
          <h1>Events</h1>
          <span class="event-count" v-if="filteredEvents.length">({{ filteredEvents.length }})</span>
        </div>

        <div class="section-header__actions">
          <RouterLink :to="{ name: 'events.dev' }">Dev View</RouterLink>
          <BaseButton purpose="secondary" :class="{ 'is-active': sortDescending }"
            @click="sortDescending = !sortDescending">
            <ArrowUpDown class="icon" />
            {{ sortDescending ? 'Descending' : 'Ascending' }}
          </BaseButton>
          <BaseButton purpose="primary" @click="router.push({ name: 'events.create' })">
            <Plus class="icon" />
            Create Event
          </BaseButton>
        </div>
      </div>
    </template>

    <BaseToggle v-model="showFilters">Show filters</BaseToggle>
    <BaseCard v-show="showFilters">
      <div class="date-range-container">
        <DateRangeSelector v-model:fromDate="selectedFromDate" v-model:toDate="selectedToDate"
          v-model:selectedYear="selectedYear" :defaultFromDate="fromDate" :showYearSelector="true"
          :availableYears="availableYears" />
      </div>

      <div class="search-box">
        <Search class="search-icon" />
        <input type="text" v-model="searchQuery" placeholder="Search venues, towns, counties..." class="search-input">
      </div>

      <div class="acts-filter">
        <div class="acts-filter__chips">
          <BaseButton v-for="act in availableActs" :key="act.id" @click="toggleAct(act.id)"
            :class="['act-chip', { 'act-chip--selected': selectedActs.includes(act.id) }]" purpose="secondary"
            size="compact">
            {{ act.name }}
          </BaseButton>
          <BaseButton v-if="selectedActs.length || searchQuery" @click="() => { selectedActs = []; searchQuery = '' }"
            purpose="danger" size="compact">
            <X class="icon" />
            Clear
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <div v-if="isLoadingEvents || areDetailsLoading" class="loading">
      <LoadingSpinner />
      <p class="loading-text">
        {{ isLoadingEvents ? 'Loading events...' : 'Loading event details...' }}
      </p>
    </div>
    <div v-else-if="error" class="error">
      {{ error }}
    </div>
    <div v-else-if="!events?.length" class="no-events">
      <p>No events found</p>
    </div>
    <div v-else class="events-list">
      <EventCard v-for="event in filteredEvents" :key="event.id" :event="event as Event" view-mode="list"
        class="events__item" />
    </div>
  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
  justify-content: space-between;
}

.title-group {
  display: flex;
  align-items: baseline;
  gap: var(--space-s);
}

.event-count {
  color: var(--color-text-muted);
  font-size: var(--step-0);
}

.section-header__actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
}

.is-active {
  background: var(--color-brand);
  color: var(--color-background);
}

/* Remove size-specific button styles as they're handled by BaseButton */
.icon {
  width: var(--step-0);
  height: var(--step-0);
}

.act-chip {
  font-size: var(--step--1);
}

.act-chip--selected {
  background: var(--color-brand-dark);
  border-color: var(--color-brand-light);
  color: var(--color-background);
}

.act-chip--selected:hover {
  opacity: 0.9;
}

.date-range-container {
  margin-bottom: var(--space-m);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--space-m);
}

.search-box {
  position: relative;
  width: 100%;
  margin-bottom: var(--space-m);
}

.search-icon {
  position: absolute;
  right: var(--space-m);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-muted);
  width: 1.2em;
  height: 1.2em;
}

.search-input {
  width: 100%;
  padding: var(--space-s) var(--space-m) var(--space-s) var(--space-xl);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-accent);
}

.acts-filter {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.acts-filter__chips {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  margin-top: var(--space-l);
}

.loading,
.error {
  text-align: center;
  padding: var(--space-l);
}

.error {
  color: var(--color-text-danger);
}

.loading-text {
  margin-top: var(--space-s);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.no-events {
  text-align: center;
  padding: var(--space-l);
  color: var(--color-text-muted);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-m);
  }

  .section-header__actions {
    width: 100%;
  }
}
</style>
