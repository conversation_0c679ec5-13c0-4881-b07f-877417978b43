<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useEvents } from '@/composables/useEvents'
import EventForm from '@/components/events/EventForm.vue'
import { Event, type EventNotes } from '@/models/Event'

const route = useRoute()
const router = useRouter()
const { createEvent, updateEvent, subscribeToEvent, currentEvent, isLoading, areDetailsLoading } = useEvents()
const isSubmitting = ref(false)

const isEditMode = computed(() => !!route.params.id)
const pageTitle = computed(() => isEditMode.value ? 'Edit Event' : 'Create Event')

// Update document title
watch(() => pageTitle.value, (newTitle) => {
  document.title = newTitle
}, { immediate: true })

onMounted(async () => {
  if (isEditMode.value) {
    const eventId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id
    await subscribeToEvent(eventId)
  } else {
    isLoading.value = false
  }
})

const showForm = computed(() => {
  if (isEditMode.value) {
    return !areDetailsLoading.value && currentEvent.value !== undefined
  }
  return true
})

const handleSubmit = async (eventData: Partial<Event>) => {
  isSubmitting.value = true
  try {
    const eventId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id

    // Prepare the notes data ensuring proper typing and structure
    const notesData: EventNotes = {
      fee: {
        amount: eventData.notes?.fee?.amount !== undefined ? Number(eventData.notes.fee.amount) : 0,
        paid: eventData.notes?.fee?.paid ?? false,
        date: eventData.notes?.fee?.date ?? null
      },
      deposit: {
        amount: eventData.notes?.deposit?.amount !== undefined ? Number(eventData.notes.deposit.amount) : 0,
        paid: eventData.notes?.deposit?.paid ?? false,
        date: eventData.notes?.deposit?.date ?? null
      },
      agent: eventData.notes?.agent ?? null
    }

    const updates: Partial<Event> = {
      ...eventData,
      notes: notesData
    }

    if (isEditMode.value) {
      await updateEvent(eventId, updates)
    } else {
      await createEvent(updates)
    }

    // Only navigate if successful
    router.push('/events')
    return true
  } catch (error) {
    console.error(`Failed to ${isEditMode.value ? 'update' : 'create'} event:`, error)
    return false
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="section-header">
        <h1>{{ pageTitle }}</h1>
        <RouterLink v-if="isEditMode" :to="{ name: 'events.show', params: { id: route.params.id } }" class="back-link">
          ← Back to Event
        </RouterLink>
      </div>
    </template>

    <LoadingSpinner v-if="isLoading || areDetailsLoading">
      {{ isLoading ? 'Loading event...' : 'Loading event details...' }}
    </LoadingSpinner>

    <EventForm v-if="!isEditMode" :is-submitting="isSubmitting" :date="route.query.date as string | null"
      @submit="handleSubmit" />

    <EventForm v-else-if="showForm" :initial-values="currentEvent as Partial<Event>" is-edit-mode
      :is-submitting="isSubmitting" @submit="handleSubmit" />

  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-link {
  font-size: 0.9em;
  color: var(--color-text-secondary);
  text-decoration: none;
}

.back-link:hover {
  color: var(--color-primary);
}
</style>
