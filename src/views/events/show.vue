<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEvents } from '@/composables/useEvents'
import { MapPin, Calendar, Clock, Globe, Mail, Phone, Pencil, Trash2 } from 'lucide-vue-next'

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const {
  currentEvent: event,
  isLoading: isLoadingEvent,
  areDetailsLoading,
  error,
  deleteEvent,
  cleanup,
  subscribeToEvent
} = useEvents()

// Actions
const handleDelete = async () => {
  if (!event.value) return

  const success = await deleteEvent(event.value.id)
  if (success) {
    router.push({ name: 'events.index' })
  }
}

// Lifecycle hooks
onMounted(async () => {
  await subscribeToEvent(props.id)
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <div v-if="isLoadingEvent || areDetailsLoading" class="loading-container">
    <LoadingSpinner />
    <p>{{ isLoadingEvent ? 'Loading event...' : 'Loading event details...' }}</p>
  </div>

  <div v-else-if="error" class="error-container">
    <p class="error-message">{{ error }}</p>
    <BaseButton @click="router.push({ name: 'events.index' })">
      Return to Events
    </BaseButton>
  </div>

  <BaseSection v-else-if="event" class="event-card">
    <template #header>
      <RouterLink :to="{ name: 'events.index' }" class="back-button">
        ← Events
      </RouterLink>
      <h1 v-if="event.title">{{ event.shortTitle() }}</h1>
      <h1 v-else class="no-title">No title has been set</h1>
      <div class="date">
        <Calendar class="icon" />
        <span>{{ event.formattedDate() }}</span>
        at
        <Clock class="icon" />
        <span>{{ event.startTime() }}</span>
      </div>
    </template>
    <div class="event-details">
      <h4>{{ event.actDisplayNames() }}</h4>
      <div>
        <div class="venue" v-if="event.venueDetails">
          <MapPin class="icon" />
          <span>{{ event.venueName() }}, {{ event.venueAddress() }}</span>
        </div>
        <div class="venue-contact">
          <a v-if="event.venueDetails?.website" :href="event.venueDetails.website" target="_blank"
            rel="noopener noreferrer" class="venue-link">
            <Globe class="icon" />
            <span>Website</span>
          </a>
          <a v-if="event.venueDetails?.email" :href="`mailto:${event.venueDetails.email}`" class="venue-link">
            <Mail class="icon" />
            <span>{{ event.venueDetails.email }}</span>
          </a>
          <a v-if="event.venueDetails?.phone" :href="`tel:${event.venueDetails.phone}`" class="venue-link">
            <Phone class="icon" />
            <span>{{ event.venueDetails.phone }}</span>
          </a>
        </div>
      </div>
    </div>
    <div class="event-description" v-html="event.description"></div>
    <template #footer>
      <div class="event-footer">
        <small class="event-id">
          <span v-if="event.isPrivate" class="is-private">Private</span>
          ID: {{ event.id }}
        </small>
        <div class="event-actions">
          <BaseButton @click="router.push({ name: 'events.edit', params: { id: event.id } })" purpose="primary"
            aria-label="Edit Event" title="Edit Event">
            <Pencil class="icon" />
          </BaseButton>
          <BaseButton @click="handleDelete" purpose="danger" aria-label="Delete Event" title="Delete Event">
            <Trash2 class="icon" />
          </BaseButton>
        </div>
      </div>
    </template>
  </BaseSection>
</template>

<style scoped>
.back-button {
  display: inline-block;
  font-size: var(--step-0);
  margin-block-end: var(--space-xs);
}

.event-card {
  padding: var(--space-s);
}

.no-title {
  color: var(--color-text-muted);
  font-weight: 700;
  font-size: var(--step-3);
}

.date {
  font-size: var(--step-1);
  color: var(--color-text-muted);
}

.event-details {
  display: grid;
  gap: var(--space-s);
  margin-block-end: var(--space-3xs);

  .venue {
    display: flex;
    align-items: center;
    gap: var(--space-3xs);

    >* {
      flex-shrink: 0;
      width: fit-content;
    }
  }

  .venue-contact {
    display: flex;
    flex-wrap: wrap;
    font-size: var(--step--1);
    gap: 0 var(--space-xs);

    >* {
      display: flex;
      align-items: center;
      gap: var(--space-3xs);
    }
  }
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: end;
}

.event-id {
  font-size: var(--step--2);
  color: var(--color-text-muted);
}

.is-private {
  padding: var(--space-3xs) var(--space-2xs);
  background-color: var(--color-danger-dark);
  border-radius: var(--space-3xs);
  color: var(--color-text);
  margin-inline-end: var(--space-3xs);
}

.event-actions {
  display: flex;
  gap: var(--space-2xs);
}
</style>
