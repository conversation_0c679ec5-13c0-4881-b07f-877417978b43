<script setup lang="ts">
import { useEvents } from '@/composables/useEvents'
import { Search, X, ArrowUpDown, Plus, PoundSterling, Percent, Calendar } from 'lucide-vue-next'
import DateRangeSelector from '../../components/DateRangeSelector.vue'
import ManagePaymentsCard from '../../components/payments/ManagePaymentsCard.vue'
import { ref, onUnmounted, watch, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Event } from '@/models/Event'

const router = useRouter()

// Initialize data with default values
const fromDate = ref(new Date())
const selectedFromDate = ref<Date | null>(null)
const selectedToDate = ref<Date | null>(null)
const selectedYear = ref<string>('')
const sortDescending = ref(true) // Default to newest first
const searchQuery = ref<string>('')
const selectedActs = ref<string[]>([])
const showFilters = ref(false)

// Payment filters
const showNoFee = ref(true)
const showUnpaidFee = ref(true)
const showUnpaidDeposit = ref(true)

// Initialize useEvents with default filters
const {
  events,
  isLoading: isLoadingEvents,
  areDetailsLoading,
  error,
  subscribeToEvents,
  updateEvent,
  cleanup
} = useEvents({
  sortDescending: true
})

// Computed property to get all available years from raw events data
const availableYears = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  const years: Set<number> = new Set()
  events.value.forEach(event => {
    if (event?.when instanceof Date) {
      years.add(event.when.getFullYear())
    }
  })
  return Array.from(years).sort((a, b) => b - a)
})

// Get unique acts from all events
const availableActs = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  const actsMap = new Map()
  events.value.forEach((event) => {
    if (event?.actDetails) {
      event.actDetails.forEach(act => {
        if (act?.id && !actsMap.has(act.id)) {
          actsMap.set(act.id, act)
        }
      })
    }
  })

  return Array.from(actsMap.values())
})

// Filter events based on search, acts, and payment status
const filteredEvents = computed(() => {
  if (!events.value || !Array.isArray(events.value)) return []

  let filtered = events.value

  // Filter by selected acts
  if (selectedActs.value.length > 0) {
    filtered = filtered.filter((event) => {
      return event?.acts && selectedActs.value.some(actId => event.acts.includes(actId))
    })
  }

  // Filter by search query
  if (searchQuery.value) {
    filtered = filtered.filter((event) => {
      return event?.matchesVenueSearch && event.matchesVenueSearch(searchQuery.value)
    })
  }

  // Filter by payment status
  filtered = filtered.filter((event) => {
    // Events with no fee set
    if (showNoFee.value && (!event.fee() || event.fee() === 0)) {
      return true
    }

    // Events with unpaid fee
    if (showUnpaidFee.value && event.fee() > 0 && !event.isFeePaid()) {
      return true
    }

    // Events with unpaid deposit
    if (showUnpaidDeposit.value && event.deposit() > 0 && !event.isDepositPaid()) {
      return true
    }

    return false
  })

  return filtered
})

// Initialize data
async function initializeEvents() {
  try {
    await subscribeToEvents({
      fromDate: selectedFromDate.value ?? fromDate.value,
      toDate: selectedToDate.value ?? undefined,
      sortDescending: sortDescending.value,
      includePaid: true // We need to include paid events to filter them ourselves
    })
  } catch (err) {
    console.error('Failed to initialize events:', err)
  }
}

// Watch for filter changes
watch([selectedFromDate, selectedToDate, sortDescending], async () => {
  await initializeEvents()
})

onMounted(async () => {
  await initializeEvents()
})

// Cleanup subscription on component unmount
onUnmounted(() => {
  cleanup()
})

// Toggle act selection
function toggleAct(actId: string) {
  const index = selectedActs.value.indexOf(actId)
  if (index === -1) {
    selectedActs.value.push(actId)
  } else {
    selectedActs.value.splice(index, 1)
  }
}

// Handle event updates from the payment card
async function handleEventUpdate(eventId: string, updates: any) {
  await updateEvent(eventId, updates)
}
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="section-header">
        <div class="title-group">
          <h1>Manage Payments</h1>
        </div>

        <div class="section-header__actions">
          <BaseButton purpose="secondary" :class="{ 'is-active': sortDescending }"
            @click="sortDescending = !sortDescending">
            <ArrowUpDown class="icon" />
            {{ sortDescending ? 'Newest first' : 'Oldest first' }}
          </BaseButton>
          <BaseButton purpose="primary" @click="router.push({ name: 'events.create' })">
            <Plus class="icon" />
            Create Event
          </BaseButton>
        </div>
      </div>
    </template>

    <BaseToggle v-model="showFilters">Show filters</BaseToggle>
    <BaseCard v-show="showFilters">
      <div class="date-range-container">
        <DateRangeSelector v-model:fromDate="selectedFromDate" v-model:toDate="selectedToDate"
          v-model:selectedYear="selectedYear" :defaultFromDate="fromDate" :showYearSelector="true"
          :availableYears="availableYears" />
      </div>

      <div class="search-box">
        <Search class="search-icon" />
        <input type="text" v-model="searchQuery" placeholder="Search venues, towns, counties..." class="search-input">
      </div>

      <div class="payment-filters">
        <h3>Payment Status</h3>
        <div class="payment-filter-options">
          <BaseToggle v-model="showNoFee" size="compact">
            <PoundSterling class="icon" /> No fee set
          </BaseToggle>
          <BaseToggle v-model="showUnpaidFee" size="compact">
            <PoundSterling class="icon" /> Unpaid fees
          </BaseToggle>
          <BaseToggle v-model="showUnpaidDeposit" size="compact">
            <Percent class="icon" /> Unpaid deposits
          </BaseToggle>
        </div>
      </div>

      <div class="acts-filter">
        <h3>Filter by Act</h3>
        <div class="acts-filter__chips">
          <BaseButton v-for="act in availableActs" :key="act.id" @click="toggleAct(act.id)"
            :class="['act-chip', { 'act-chip--selected': selectedActs.includes(act.id) }]" purpose="secondary"
            size="compact">
            {{ act.name }}
          </BaseButton>
          <BaseButton v-if="selectedActs.length || searchQuery" @click="() => { selectedActs = []; searchQuery = '' }"
            purpose="danger" size="compact">
            <X class="icon" />
            Clear
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <div v-if="isLoadingEvents || areDetailsLoading" class="loading">
      <LoadingSpinner />
      <p class="loading-text">
        {{ isLoadingEvents ? 'Loading events...' : 'Loading event details...' }}
      </p>
    </div>
    <div v-else-if="error" class="error">
      {{ error }}
    </div>
    <div v-else-if="!filteredEvents.length" class="no-events">
      <p>No events found matching your filters</p>
    </div>
    <div v-else class="payment-events-list">
      <ManagePaymentsCard v-for="event in filteredEvents" :key="event.id" :event="event as Event"
        @update-event="handleEventUpdate" />
    </div>
  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
  justify-content: space-between;
}

.title-group {
  display: flex;
  align-items: baseline;
  gap: var(--space-s);
}

.event-count {
  color: var(--color-text-muted);
  font-size: var(--step-0);
}

.section-header__actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
}

.is-active {
  background: var(--color-brand);
  color: var(--color-background);
}

.icon {
  width: var(--step-0);
  height: var(--step-0);
}

.payment-filters {
  margin: var(--space-m) 0;
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--space-m);
}

.payment-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-m);
  margin-top: var(--space-s);
}

.act-chip {
  font-size: var(--step--1);
}

.act-chip--selected {
  background: var(--color-brand-dark);
  border-color: var(--color-brand-light);
  color: var(--color-background);
}

.date-range-container {
  margin-bottom: var(--space-m);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--space-m);
}

.search-box {
  position: relative;
  width: 100%;
  margin-bottom: var(--space-m);
}

.search-icon {
  position: absolute;
  right: var(--space-m);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-muted);
  width: 1.2em;
  height: 1.2em;
}

.search-input {
  width: 100%;
  padding: var(--space-s) var(--space-m) var(--space-s) var(--space-xl);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-accent);
}

.acts-filter {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.acts-filter__chips {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
}

.payment-events-list {
  display: grid;
  gap: var(--space-xs);
  margin-top: var(--space-l);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-m);
  }

  .section-header__actions {
    width: 100%;
  }
}
</style>
