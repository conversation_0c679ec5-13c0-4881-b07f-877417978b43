<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useActs } from '@/composables/useActs'
import { useArtists } from '@/composables/useArtists'
import { ArrowLeft, Globe, Users } from 'lucide-vue-next'
import EntityImage from '@/components/base/EntityImage.vue'

const route = useRoute()
const {
  currentAct: act,
  isLoading,
  subscribeToAct,
  cleanup
} = useActs()
const { artists } = useArtists()

const imageLoaded = ref(false)
const imageError = ref(false)

const handleImageLoad = () => {
  imageLoaded.value = true
}

const handleImageError = () => {
  imageError.value = true
}

const sortArtistsByDisplayName = (artistIds: string[]) => {
  return [...artistIds].sort((a, b) => {
    const artistA = artists.value.find(artist => artist.id === a)?.displayName.split(' ')[1] || '';
    const artistB = artists.value.find(artist => artist.id === b)?.displayName.split(' ')[1] || '';
    return artistA.localeCompare(artistB);
  });
};

onMounted(async () => {
  await subscribeToAct(route.params.id as string)
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <!-- Navigation -->
  <nav class="nav">
    <RouterLink :to="{ name: 'acts.index' }" class="nav-link">
      <ArrowLeft class="icon" />
      <span>Back to Acts</span>
    </RouterLink>
  </nav>

  <div v-if="isLoading" class="loading">
    <LoadingSpinner />
  </div>

  <template v-else-if="act">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero__grid">
        <!-- Badge Logo -->
        <EntityImage v-if="act.getLogoUrl('badge')" :publicId="act.getLogoUrl('badge')!"
          :alt="`${act.getDisplayName()} badge logo`" height="120" class="hero__badge-logo" />

        <!-- Text Logo -->
        <EntityImage v-if="act.getLogoUrl('text')" :publicId="act.getLogoUrl('text')!"
          :alt="`${act.getDisplayName()} text logo`" height="80" class="hero__text-logo" />

        <!-- Main photo -->
        <EntityImage v-if="act.getPhotoUrl()" :publicId="act.getPhotoUrl()!" :alt="act.getDisplayName()" height="400"
          :fit="true" class="hero__image" @load="handleImageLoad" @error="handleImageError" />
      </div>
    </section>

    <!-- Primary Info -->
    <section class="section">
      <div class="section__header">
        <h1 class="section__title">{{ act.getDisplayName() }}</h1>
      </div>
      <p class="section__description">{{ act.getDescription() }}</p>
      <p v-if="act.getDefaultGigDescription()" class="section__gig-description">
        {{ act.getDefaultGigDescription() }}
      </p>
    </section>

    <!-- Contact Info -->
    <section class="section" v-if="act.getWebsite()">
      <h2 class="section__title">Contact Information</h2>
      <div class="info-grid">
        <div class="info-card">
          <Globe class="info-card__icon" />
          <div class="info-card__content">
            <a :href="act.getWebsite()" target="_blank" rel="noopener">Visit Website</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Artists -->
    <section class="section" v-if="act.getArtistIds().length">
      <h2 class="section__title">Artists</h2>
      <div class="info-grid">
        <div v-for="artistId in sortArtistsByDisplayName(act.getArtistIds())" :key="artistId" class="info-card">
          <Users class="info-card__icon" />
          <div class="info-card__content">
            <!-- display artist name -->
            <div class="info-card__content">
              {{artists.find(artist => artist.id === artistId)?.displayName}}
            </div>
            <div class="info-card__instruments">
              {{ act.getArtistInstruments(artistId).join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </section>
  </template>
</template>

<style scoped>
.nav {
  margin-bottom: var(--space-xl);
}

.nav-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2xs);
  color: var(--color-text-muted);
  font-weight: 500;
  transition: color var(--transition-in);
}

.nav-link:hover {
  color: var(--color-text);
}

.hero {
  margin-bottom: var(--space-2xl);
  display: grid;
  gap: var(--space-xl);
}

.hero__grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-template-rows: auto auto;
  gap: var(--space-m);
}

.hero__badge-logo {
  grid-column: 1;
  grid-row: 1;
  width: 100%;
  height: auto;
  justify-self: center;
}

.hero__text-logo {
  grid-column: 1;
  grid-row: 2;
  width: 100%;
  height: auto;
  justify-self: center;
}

.hero__image {
  grid-column: 2;
  grid-row: 1 / span 2;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

/* Tablet layout */
@media (max-width: 1024px) {
  .hero__grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto;
  }

  .hero__badge-logo {
    grid-column: 1;
    grid-row: 1;
  }

  .hero__text-logo {
    grid-column: 2;
    grid-row: 1;
  }

  .hero__image {
    grid-column: 1 / span 2;
    grid-row: 2;
    aspect-ratio: 16/9;
  }
}

/* Mobile layout */
@media (max-width: 768px) {
  .hero__grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .hero__badge-logo {
    grid-column: 1;
    grid-row: 1;
  }

  .hero__text-logo {
    grid-column: 1;
    grid-row: 2;
  }

  .hero__image {
    grid-column: 1;
    grid-row: 3;
  }
}

.section {
  margin-bottom: var(--space-2xl);
}

.section__header {
  display: flex;
  align-items: center;
  gap: var(--space-m);
  margin-bottom: var(--space-l);
}

.section__title {
  font-size: var(--step-2);
  color: var(--color-heading);
  margin: 0;
}

.section__description {
  font-size: var(--step-1);
  color: var(--color-text);
  line-height: 1.6;
  margin-bottom: var(--space-l);
}

.section__gig-description {
  font-size: var(--step-0);
  color: var(--color-text-muted);
  line-height: 1.6;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-m);
}

.info-card {
  display: flex;
  align-items: center;
  gap: var(--space-m);
  padding: var(--space-l);
  background: var(--color-surface-mute);
  border-radius: var(--radius-l);
  transition: all var(--transition-in);
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.info-card__icon {
  color: var(--color-brand);
  width: 24px;
  height: 24px;
}

.info-card__label {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  margin-bottom: var(--space-3xs);
}

.info-card__content {
  flex: 1;
}

.info-card__instruments {
  margin-top: var(--space-2xs);
  font-size: var(--step--1);
  color: var(--color-text-muted);
  text-transform: capitalize;
}

.info-card__content a {
  color: var(--color-brand);
  text-decoration: none;
}

.info-card__content a:hover {
  text-decoration: underline;
}

.loading {
  display: grid;
  place-items: center;
  min-height: 50vh;
}
</style>
