w
<script setup lang="ts">
import { useActs } from '@/composables/useActs'
import { Plus } from 'lucide-vue-next'
import { Act } from '@/models/Act'
import ActCard from '@/components/ActCard.vue'
import { onMounted, onUnmounted } from 'vue'

const { acts, isLoading, subscribeToActs, cleanup } = useActs()

onMounted(() => {
  subscribeToActs()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="section-header">
        <div class="title-group">
          <h1>Acts</h1>
        </div>
        <div class="section-header__actions">
          <BaseButton purpose="primary" :to="{ name: 'acts.create' }">
            <Plus class="icon" />
            Add Act
          </BaseButton>
        </div>
      </div>
    </template>

    <div v-if="isLoading" class="loading-state">
      <LoadingSpinner />
      <p>Loading acts...</p>
    </div>

    <div v-else-if="!acts.length" class="empty-state">
      <h2>No Acts Yet</h2>
      <p>Get started by adding your first entertainment act.</p>
      <BaseButton purpose="primary" :to="{ name: 'acts.create' }">
        <Plus class="icon" />
        New Act
      </BaseButton>
    </div>

    <div v-else class="acts-grid">
      <ActCard v-for="act in acts" :key="act.id" :act="act as Act" />
    </div>
  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
  justify-content: space-between;
}

.title-group {
  display: flex;
  align-items: baseline;
  gap: var(--space-s);
}

.subtitle {
  color: var(--color-text-muted);
  font-size: var(--step-0);
}

.section-header__actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  align-items: center;
}

.icon {
  width: var(--step-0);
  height: var(--step-0);
}

.acts-grid {
  display: grid;
  gap: var(--space-l);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  width: 100%;
}

.acts-grid>* {
  min-width: 0;
  width: 100%;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: var(--space-l);
  color: var(--color-text-muted);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-m);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-m);
}

.empty-state h2 {
  color: var(--color-text);
  margin: 0;
}

@media (max-width: 768px) {
  .acts-page {
    padding: var(--space-s);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-m);
  }

  .section-header__actions {
    width: 100%;
  }
}
</style>
