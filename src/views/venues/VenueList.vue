<script setup lang="ts">
import VenueItem from '@/views/venues/VenueItem.vue'
import { Venue } from '@/models/Venue'

type Props = {
  venues: Venue[]
}

defineProps<Props>()
</script>

<template>
  <ul class="venues">
    <li v-for="venue in venues" :key="venue.id" class="venues__item">
      <VenueItem :venue="venue" />
    </li>
  </ul>
</template>

<style scoped>
.venues {
  list-style: none;
  display: grid;
  gap: 0.8rem 1rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  grid-template-rows: 1fr auto;
  padding: 0;
}

.venues__item {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 2;
}
</style>
