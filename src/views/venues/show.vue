<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useVenues } from '../../composables/useVenues'
import { MapPin, Phone, Mail, Globe, ArrowLeft, Calendar, PoundSterling, PlusCircle, Pencil, Trash2 } from 'lucide-vue-next'
import { Event } from '@/models/Event'
import { Venue } from '@/models/Venue'
import EntityActions from '@/components/base/EntityActions.vue'

const router = useRouter()
const route = useRoute()
const { fetchVenue, fetchVenueEvents, deleteVenue } = useVenues()

const venue = ref<Venue | null>(null)
const isLoading = ref(true)
const events = ref<Event[]>([])

// Add handler for delete action
async function handleDelete() {
  if (!venue.value) return
  const success = await deleteVenue(venue.value.id)
  if (success) {
    router.push({ name: 'venues.index' })
  }
}

// Add handler for edit action
function handleEdit() {
  if (!venue.value) return
  router.push({ name: 'venues.edit', params: { id: venue.value.id } })
}

// Computed properties for statistics
const totalBookings = computed(() => events.value.length)

async function fetchVenueDetails() {
  isLoading.value = true
  try {
    const venueData = await fetchVenue(typeof route.params.id === 'string' ? route.params.id : route.params.id[0])
    if (venueData) {
      venue.value = venueData
      events.value = await fetchVenueEvents(venueData.id)
    }
  } catch (error) {
    console.error('Error fetching venue details:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchVenueDetails()
})

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
  }).format(amount)
}

// Function to get next viable date with same time
function getNextViableDate(lastDate: Date) {
  const nextDate = new Date(lastDate)
  const now = new Date()

  // Set minimum 24 hours in the future
  const minDate = new Date(now)
  minDate.setHours(now.getHours() + 24)

  // If the next date with same time is less than 24 hours away,
  // move it to the next day
  while (nextDate < minDate) {
    nextDate.setDate(nextDate.getDate() + 1)
  }

  return nextDate
}

// Function to create new event from last gig
async function createFromLastGig() {
  if (!events.value.length) return

  // Get next viable date with same time as last gig
  const lastGig = events.value.reduce((last, current) => {
    return current.when.toDate() > last.when.toDate() ? current : last
  }, events.value[0])
  const nextDate = getNextViableDate(lastGig.when.toDate())

  // Get the template data from last gig
  const templateData = {
    title: lastGig.title || '',
    description: lastGig.description || '',
    duration: lastGig.duration || 180,
    when: nextDate,
    venue: venue.value?.id,
    venueDetails: {
      id: venue.value?.id,
      name: venue.value?.getName(),
      address: venue.value?.address.toString()
    },
    acts: lastGig.acts || [],
    actDetails: lastGig.actDetails || [],
    isPrivate: lastGig.isPrivate || false,
    status: 'draft',
    notes: {
      fee: venue.value?.notes?.fee ? {
        amount: Number(venue.value.notes.fee),
        paid: false
      } : null,
      deposit: venue.value?.notes?.deposit ? {
        amount: Number(venue.value.notes.deposit),
        paid: false
      } : null,
      agent: venue.value?.notes?.agentId ? {
        agent: venue.value.notes.agentId,
        percentage: Number(venue.value.notes.percentage)
      } : null
    }
  }

  // Navigate to create event with template data
  router.push({
    name: 'events.create',
    query: {
      template: btoa(JSON.stringify(templateData))
    }
  })
}
</script>

<template>
  <BaseSection v-if="venue" class="venue">
    <header class="page-header">
      <RouterLink :to="{ name: 'venues.index' }" class="back-button">
        <ArrowLeft class="icon" />
        Back to Venues
      </RouterLink>
    </header>

    <div class="venue-content">
      <BaseCard>
        <template #header>
          <h1>{{ venue.getName() }}</h1>
        </template>

        <div class="venue-info">
          <BaseSection>
            <div class="info-section">
              <div class="info-item">
                <MapPin class="icon" />
                <span>{{ venue.getFormattedAddress() }}</span>
              </div>

              <div v-if="venue.phone" class="info-item">
                <Phone class="icon" />
                <a :href="`tel:${venue.phone}`">{{ venue.phone }}</a>
              </div>

              <div v-if="venue.email" class="info-item">
                <Mail class="icon" />
                <a :href="`mailto:${venue.email}`">{{ venue.email }}</a>
              </div>

              <div v-if="venue.website" class="info-item">
                <Globe class="icon" />
                <a :href="venue.website" target="_blank" rel="noopener noreferrer">
                  {{ venue.website }}
                </a>
              </div>
            </div>
          </BaseSection>

          <BaseSection title="Statistics">
            <div class="info-section">
              <div class="info-item">
                <Calendar class="icon" />
                <details class="bookings-details">
                  <summary>{{ totalBookings }} total bookings</summary>
                  <ul class="bookings-list">
                    <li v-for="event in events" :key="event.id" class="booking-item">
                      <RouterLink :to="{ name: 'events.show', params: { id: event.id } }" class="booking-link">
                        {{ event.formattedDate() }}
                      </RouterLink>
                    </li>
                  </ul>
                </details>
              </div>

              <div v-if="events.length" class="info-item">
                <Calendar class="icon" />
                <div class="last-gig">
                  <RouterLink :to="{ name: 'events.show', params: { id: events[events.length - 1].id } }" class="link">
                    Last gig: {{ events[events.length - 1].formattedDate() }}
                  </RouterLink>
                  <BaseButton @click="createFromLastGig" class="book-again-button" title="Book again with same details">
                    <PlusCircle class="icon" />
                    <span>Book Again</span>
                  </BaseButton>
                </div>
              </div>

              <div class="info-item">
                <PoundSterling class="icon" />
                <details class="bookings-details">
                  <summary>
                    {{formatCurrency(events.reduce((sum, event) => sum + (event.fee() || 0), 0))}} total revenue
                  </summary>
                  <ul class="bookings-list">
                    <li v-for="event in events" :key="event.id" class="booking-item"
                      :class="{ 'no-fee': !event.fee() }">
                      <RouterLink :to="{ name: 'events.show', params: { id: event.id } }" class="booking-link">
                        {{ event.formattedDate() }}: {{ event.fee() ? formatCurrency(event.fee()) : 'No fee set' }}
                      </RouterLink>
                    </li>
                  </ul>
                </details>
              </div>
            </div>
          </BaseSection>
        </div>

        <template #footer>
          <div class="actions">
            <BaseButton @click="handleEdit" purpose="primary" aria-label="Edit Venue" title="Edit Venue">
              <Pencil class="icon" />
            </BaseButton>
            <BaseButton @click="handleDelete" purpose="danger" aria-label="Delete Venue" title="Delete Venue">
              <Trash2 class="icon" />
            </BaseButton>
          </div>
        </template>
      </BaseCard>
    </div>
  </BaseSection>
</template>

<style scoped>
.venue {
  display: contents;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-light);
  text-decoration: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--color-background-soft);
  color: var(--color-text);
}

.venue-content {
  max-width: 800px;
  margin: 0 auto;
}

/* Move h1 styles to card header */
:deep(.card-header) h1 {
  margin: 0;
  color: var(--color-heading);
}

.venue-info {
  display: grid;
  gap: 2rem;
}

.info-section {
  display: grid;
  gap: 2rem;
}

.info-item {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: start;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  color: var(--color-text);
  font-size: var(--step--1);
}

.info-item:not(:last-child) {
  border-bottom: 1px solid var(--color-border);
}

.info-item a {
  color: var(--color-text);
  text-decoration: none;
}

.info-item a:hover {
  color: var(--color-accent);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex: 0 0 auto;
}

.notes-container {
  flex: 1;
}

.add-note-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.new-note-form {
  padding: 1rem;
  margin-bottom: 1rem;
}

.note-title-input {
  margin-bottom: 1rem;
}

.note-content-editor {
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.note-item {
  padding: 0;
}

.note-details {
  padding: 1rem;
}

.note-header {
  display: flex;
  gap: var(--space-s);
  align-items: flex-end;
  margin-bottom: 0.5rem;
}

.note-title {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.note-title h3 {
  margin: 0;
  font-size: var(--step-0);
  color: var(--color-heading);
  font-weight: 500;
}

.note-metadata {
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  gap: var(--space-s);
  color: var(--color-text-muted);
  font-size: var(--step--2);
}

.note-author {
  font-weight: 500;
}

.note-timestamp {
  font-style: italic;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
  margin-inline-start: auto;
}

.note-content {
  margin-top: 0.75rem;
  color: var(--color-text);
  line-height: 1.5;
}

.note-content :deep(p) {
  margin: 0.5rem 0;
}

.note-content :deep(p:first-child) {
  margin-top: 0;
}

.note-content :deep(p:last-child) {
  margin-bottom: 0;
}

.no-notes {
  color: var(--color-text-light);
  font-style: italic;
}

.error-message {
  text-align: center;
  color: var(--color-text-danger);
  margin: 2rem 0;
}

.loading {
  text-align: center;
  color: var(--color-text-light);
  margin: 2rem 0;
}

.link {
  color: var(--color-text);
  text-decoration: none;
}

.link:hover {
  color: var(--color-accent);
}

.last-gig {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.book-again-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.book-again-button:hover {
  background: var(--color-background-mute);
  color: var(--color-accent);
}

.book-again-button .icon {
  width: 1em;
  height: 1em;
}

.bookings-details {
  cursor: pointer;
}

.bookings-details summary {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  padding-right: 1.5rem;
}

.bookings-details summary::after {
  content: '▼';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8em;
  color: var(--color-text-light);
  transition: transform 0.2s ease;
}

.bookings-details[open] summary::after {
  transform: translateY(-50%) rotate(180deg);
}

.bookings-list {
  list-style: none;
  padding: 0.5rem 0 0 1.5rem;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.booking-item {
  font-size: var(--step--2);
}

.booking-link {
  color: var(--color-text-light);
  text-decoration: none;
  transition: color 0.2s ease;
}

.booking-link:hover {
  color: var(--color-accent);
}

.missing-fee {
  color: var(--color-text-light);
  font-size: var(--step--2);
  margin-left: 0.5rem;
}

.no-fee {
  color: var(--color-text-light);
  font-style: italic;
}

.no-fee .booking-link {
  color: inherit;
}

.no-fee .booking-link:hover {
  color: var(--color-accent);
}

.delete-dialog :deep(.dialog-content) {
  max-width: 400px;
}

.note-title-preview {
  margin-top: 0.5rem;
  font-weight: 500;
  color: var(--color-heading);
}

.note-details {
  background: var(--color-background-soft);
  border-radius: 0.5rem;
  padding: 1rem;
}

.note-details summary {
  list-style: none;
  cursor: pointer;
}

.note-details summary::-webkit-details-marker {
  display: none;
}

.note-details[open] summary {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
}

.note-details:not([open]) .note-header {
  margin-bottom: 0;
}
</style>
