<script setup lang="ts">
import { reactive } from 'vue'
import { Save, X } from 'lucide-vue-next'
import NotesEditor from '../../components/NotesEditor.vue'

const props = defineProps({
  venue: {
    type: Object,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'close'])

// Initialize form state
const form = reactive({
  name: props.venue ? props.venue.name : '',
  address: {
    address1: props.venue?.address?.address1 || '',
    address2: props.venue?.address?.address2 || '',
    town: props.venue?.address?.town || '',
    county: props.venue?.address?.county || '',
    postcode: props.venue?.address?.postcode || ''
  },
  phone: props.venue?.phone || '',
  email: props.venue?.email || '',
  website: props.venue?.website || '',
  notes: props.venue?.notes || {}
})

function handleSubmit(e: Event) {
  e.preventDefault()
  emit('submit', { ...form })
}

function handleCancel() {
  emit('close')
}
</script>

<template>
  <form @submit="handleSubmit" class="venue-form">
    <div class="form-section">
      <h2>Basic Details</h2>
      <div class="form-row">
        <div class="form-group grow">
          <label for="name">Venue Name</label>
          <input id="name" v-model="form.name" type="text" required :disabled="isLoading" />
        </div>
        <div class="form-group">
          <label for="phone">Phone</label>
          <input id="phone" v-model="form.phone" type="tel" :disabled="isLoading" />
        </div>
      </div>
    </div>

    <div class="form-section">
      <h2>Address</h2>
      <div class="form-group">
        <label for="address1">Address Line 1</label>
        <input id="address1" v-model="form.address.address1" type="text" required :disabled="isLoading" />
      </div>
      <div class="form-group">
        <label for="address2">Address Line 2</label>
        <input id="address2" v-model="form.address.address2" type="text" :disabled="isLoading" />
      </div>
      <div class="form-row">
        <div class="form-group grow">
          <label for="town">Town</label>
          <input id="town" v-model="form.address.town" type="text" required :disabled="isLoading" />
        </div>
        <div class="form-group grow">
          <label for="county">County</label>
          <input id="county" v-model="form.address.county" type="text" :disabled="isLoading" />
        </div>
        <div class="form-group">
          <label for="postcode">Postcode</label>
          <input id="postcode" v-model="form.address.postcode" type="text" required :disabled="isLoading" />
        </div>
      </div>
    </div>

    <div class="form-section">
      <h2>Contact Details</h2>
      <div class="form-row">
        <div class="form-group grow">
          <label for="email">Email</label>
          <input id="email" v-model="form.email" type="email" :disabled="isLoading" />
        </div>
        <div class="form-group grow">
          <label for="website">Website</label>
          <input id="website" v-model="form.website" type="url" placeholder="https://" :disabled="isLoading" />
        </div>
      </div>
    </div>

    <div class="form-section">
      <h2>Notes</h2>
      <NotesEditor v-model:notes="form.notes" :is-disabled="isLoading" />
    </div>

    <div class="form-actions">
      <BaseButton type="button" purpose="secondary" @click="handleCancel">
        <X class="icon" />
        <span>Cancel</span>
      </BaseButton>

      <BaseButton type="submit" purpose="primary" :disabled="isLoading">
        <Save class="icon" />
        <span>{{ isLoading ? 'Saving...' : 'Save Venue' }}</span>
      </BaseButton>
    </div>
  </form>
</template>

<style scoped>
.venue-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  width: 100%;
}

.form-section {
  display: grid;
  gap: var(--space-s);
  background: var(--color-background);
  padding: var(--space-m);
  border-radius: var(--radius-s);
}

.form-section h2 {
  font-size: var(--step--1);
  color: var(--color-heading);
  margin: 0;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-s);
  align-items: start;
}

.form-group.grow {
  flex-grow: 1;
  min-width: 150px;
}

.form-group label {
  font-size: var(--step--2);
  color: var(--color-text);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border-radius: var(--radius-s);
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-accent);
}

.form-group input:disabled,
.form-group textarea:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-s);
  margin-top: var(--space-m);
}

.button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-s);
  border: none;
  font-size: var(--step--1);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
}

.button--primary:not(:disabled):hover {
  filter: brightness(1.1);
}

.button--secondary {
  background: var(--color-background-mute);
  color: var(--color-text);
}

.button--secondary:not(:disabled):hover {
  background: var(--color-background-soft);
}

.icon {
  width: 1em;
  height: 1em;
  flex-shrink: 0;
}
</style>
