<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import TextLogo from '@/components/TextLogo.vue'
import { useArtist } from '@/composables/useArtist'
import { useActs } from '@/composables/useActs'
import { Artist } from '@/models/Artist'

const route = useRoute()
const { artist, isLoadingArtist, updateArtist } = useArtist(route.params.id[0])
const { acts } = useActs()

type EditingFields = {
  stageName: boolean
  firstName: boolean
  lastName: boolean
  instruments: boolean
}

const editingFields = ref<EditingFields>({
  stageName: false,
  firstName: false,
  lastName: false,
  instruments: false
})

const editedValues = ref<Partial<Artist>>({})

const startEditing = (field: keyof EditingFields) => {
  if (artist.value) {
    editedValues.value = {
      ...editedValues.value,
      [field]: artist.value[field]
    }
    editingFields.value = {
      ...editingFields.value,
      [field]: true
    }
  }
}

const cancelEditing = (field: keyof EditingFields) => {
  editingFields.value = {
    ...editingFields.value,
    [field]: false
  }
  editedValues.value = {
    ...editedValues.value,
    [field]: null
  }
}

const saveField = async (field: keyof EditingFields) => {
  if (!artist.value) return

  try {
    let value = editedValues.value[field] as string
    let values: string[] = []
    if (field === 'instruments') {
      values = value.split(',').map(i => i.trim())
    }
    await updateArtist({
      [field]: values ? values : value
    })
    editingFields.value = {
      ...editingFields.value,
      [field]: false
    }
    editedValues.value = {
      ...editedValues.value,
      [field]: null
    }
  } catch (error) {
    console.error('Error updating field:', error)
  }
}

const associatedActs = computed(() => {
  return acts.value.filter(act => act.artistIds?.includes(route.params.id[0]))
})

const showPhotoOptions = ref(false)
const isUploadingPhoto = ref(false)

const handleExistingPhotoSelect = async (photoUrl: string) => {
  try {
    // Store current photo in otherPhotoUrls if it exists
    const updatedOtherPhotos = [
      ...(artist.value?.otherPhotoUrls || [])
    ]

    if (artist.value?.photoUrl) {
      updatedOtherPhotos.unshift(artist.value?.photoUrl)
    }

    // Remove selected photo from otherPhotos
    const filteredPhotos = updatedOtherPhotos.filter(url => url !== photoUrl)

    // Update both fields
    await updateArtist(
      {
        otherPhotoUrls: filteredPhotos
      }
    )
    await updateArtist({
      photoUrl: photoUrl
    })

    showPhotoOptions.value = false
  } catch (error) {
    console.error('Error updating profile picture:', error)
  }
}

const handleNewPhotoUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    isUploadingPhoto.value = true

    // Upload to Cloudinary
    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_preset', 'artist-photos')

    const response = await fetch(
      'https://api.cloudinary.com/v1_1/dave-collison/image/upload',
      {
        method: 'POST',
        body: formData
      }
    )

    if (!response.ok) {
      throw new Error('Failed to upload image')
    }

    const data = await response.json()
    const newPhotoUrl = data.secure_url

    // Store current photo in otherPhotoUrls if it exists
    const updatedOtherPhotos = [
      ...(artist.value?.otherPhotoUrls || [])
    ]

    if (artist.value?.photoUrl) {
      updatedOtherPhotos.unshift(artist.value?.photoUrl)
    }

    // Update both fields
    await updateArtist({
      otherPhotoUrls: updatedOtherPhotos
    })
    await updateArtist({
      photoUrl: newPhotoUrl
    })
  } catch (error) {
    console.error('Error updating profile picture:', error)
  } finally {
    isUploadingPhoto.value = false
    showPhotoOptions.value = false
  }
}

const computedStageName = computed({
  get: () => editedValues.value.stageName || artist.value?.stageName,
  set: (value) => {
    editedValues.value.stageName = value
  }
})
</script>

<template>
  <p v-if="isLoadingArtist" class="loading">Loading...</p>
  <div v-else-if="artist" class="artist-content">
    <header class="artist-header">
      <RouterLink to="/artists" class="back-button">
        ← Artists
      </RouterLink>
    </header>

    <div class="artist-sections">
      <section class="artist-section artist-section--details">
        <h2>Basic Details</h2>
        <div class="artist-details">
          <div class="artist-identity">
            <div class="artist-photo editable-field">
              <img v-if="artist.photoUrl" :src="artist.photoUrl" :alt="artist.stageName" />
              <div v-else class="photo-placeholder">
                {{ artist.stageName[0] || '?' }}
              </div>

              <BaseButton class="photo-edit-overlay" @click="showPhotoOptions = true">
                <span>Change Photo</span>
              </BaseButton>
            </div>

            <div class="editable-field">
              <div v-if="!editingFields.stageName" class="field-display">
                <h3 class="stage-name">{{ artist.stageName }}</h3>
                <BaseButton class="edit-button" @click="startEditing('stageName')">Edit</BaseButton>
              </div>
              <div v-else class="edit-form">
                <input type="text" v-model="computedStageName" class="edit-input edit-input--large">
                <div class="edit-actions">
                  <BaseButton class="save-button" @click="saveField('stageName')">Save</BaseButton>
                  <BaseButton class="cancel-button" @click="cancelEditing('stageName')">Cancel</BaseButton>
                </div>
              </div>
            </div>

            <div class="editable-field">
              <div v-if="!editingFields.firstName || !editingFields.lastName" class="field-display">
                <p class="real-name">
                  {{ artist.firstName }} {{ artist.lastName }}
                </p>
                <BaseButton class="edit-button" @click="startEditing('firstName'); startEditing('lastName')">
                  Edit
                </BaseButton>
              </div>
              <div v-else class="edit-form">
                <div class="name-inputs">
                  <input type="text" v-model="editedValues.firstName" placeholder="First Name" class="edit-input">
                  <input type="text" v-model="editedValues.lastName" placeholder="Last Name" class="edit-input">
                </div>
                <div class="edit-actions">
                  <BaseButton class="save-button" @click="saveField('firstName').then(() => saveField('lastName'))">
                    Save
                  </BaseButton>
                  <BaseButton class="cancel-button" @click="cancelEditing('firstName'); cancelEditing('lastName')">
                    Cancel
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>

          <div class="artist-bio">
            <div class="bio-placeholder">
              <p class="text-muted">Bio coming soon...</p>
            </div>
          </div>

          <div class="editable-field instruments-section">
            <div v-if="!editingFields.instruments" class="field-display">
              <div class="instruments-list">
                <span v-for="instrument in artist.instruments" :key="instrument" class="instrument-tag">
                  {{ instrument }}
                </span>
              </div>
              <BaseButton class="edit-button" @click="startEditing('instruments')">Edit</BaseButton>
            </div>
            <div v-else class="edit-form">
              <input type="text" v-model="editedValues.instruments" placeholder="e.g. guitar, vocals, piano"
                class="edit-input">
              <div class="edit-actions">
                <BaseButton class="save-button" @click="saveField('instruments')">Save</BaseButton>
                <BaseButton class="cancel-button" @click="cancelEditing('instruments')">Cancel</BaseButton>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="artist-section artist-section--acts">
        <h2>Acts</h2>
        <div v-if="associatedActs.length" class="associated-acts">
          <RouterLink v-for="act in associatedActs" :key="act.id" :to="`/acts/${act.id}`" class="associated-act">
            <TextLogo v-if="act.logoUrls?.text" :publicId="act.logoUrls.text" :alt="`${act.name} logo`" height="60"
              width="200" />
            <h3 v-else class="act-name">{{ act.displayName || act.name }}</h3>
            <p class="act-display-name">{{ act.displayName || act.name }}</p>
            <div class="act-instruments">
              <span v-for="instrument in (act.artists?.[route.params.id[0]] || [])" :key="instrument"
                class="instrument-tag">
                {{ instrument }}
              </span>
            </div>
          </RouterLink>
        </div>
        <p v-else class="text-muted">No associated acts</p>
      </section>
    </div>
  </div>
</template>

<style scoped>
.artist-header {
  margin-bottom: 2rem;
}

.back-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  text-decoration: none;
  color: var(--color-accent);
  background-color: var(--color-background-mute);
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--color-accent);
  color: var(--color-background);
}

.artist-sections {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
}

.artist-section--acts {
  width: 300px;
}

.artist-section {
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.artist-section h2 {
  color: var(--color-accent);
  margin-bottom: 1.5rem;
}

.associated-acts {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.associated-act {
  text-decoration: none;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--color-background);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.associated-act:hover {
  transform: translateX(0.5rem);
  background-color: var(--color-background-mute);
}

.act-name {
  color: var(--color-accent);
  margin: 0;
}

@media (max-width: 768px) {
  .artist-sections {
    grid-template-columns: 1fr;
  }

  .artist-section--acts {
    width: 100%;
  }
}

.artist-details {
  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-areas:
    "identity bio"
    "instruments instruments";
  gap: 2rem;
}

.artist-identity {
  grid-area: identity;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: center;
  text-align: center;
}

.artist-bio {
  grid-area: bio;
  padding: 1rem;
}

.bio-placeholder {
  background-color: var(--color-background-mute);
  border-radius: 0.5rem;
  padding: 2rem;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-border);
}

.text-muted {
  color: var(--color-text-muted);
  font-style: italic;
}

.instruments-section {
  grid-area: instruments;
  margin-top: 1rem;
}

.artist-photo {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0;
}

.stage-name {
  font-size: var(--step-2);
  color: var(--color-accent);
  margin: 0;
  margin-top: -0.5rem;
  line-height: 1;
  width: 100%;
}

.real-name {
  color: var(--color-text-muted);
  font-size: var(--step-0);
  margin: 0;
  margin-top: -0.5rem;
  line-height: 1;
  width: 100%;
}

.instruments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.instrument-tag {
  font-size: var(--step--1);
  padding: 0.2em 0.6em;
  background-color: var(--color-background-mute);
  border-radius: 1em;
  color: var(--color-text-muted);
}

.act-name {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  margin: 0;
}

.artist-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: var(--shadow);
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--color-background-mute);
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--step-4);
  font-weight: bold;
  border-radius: 50%;
}

.detail-group h3 {
  color: var(--color-text-muted);
  font-size: var(--step-0);
  margin-bottom: 0.5rem;
}

.editable-field {
  position: relative;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.editable-field:hover {
  background-color: var(--color-background-mute);
}

.field-display {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
}

.edit-button {
  opacity: 0;
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  color: var(--color-accent);
  cursor: pointer;
  transition: opacity 0.2s ease;
  font-size: var(--step--2);
}

.editable-field:hover .edit-button {
  opacity: 1;
}

.edit-form {
  display: grid;
  gap: 0.5rem;
}

.name-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.edit-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: inherit;
  font-family: inherit;
}

.edit-input--large {
  font-size: var(--step-3);
  text-align: center;
}

.edit-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.save-button,
.cancel-button {
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--step--2);
}

.save-button {
  background-color: var(--color-accent);
  color: var(--color-background);
}

.save-button:hover {
  filter: brightness(1.1);
}

.cancel-button {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.cancel-button:hover {
  background-color: var(--color-background-soft);
}

.photo-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.artist-photo:hover .photo-overlay {
  opacity: 1;
}

.photo-overlay__content {
  color: white;
  font-size: var(--step--1);
  text-align: center;
  padding: 0.5em;
}

.photo-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.photo-input:disabled {
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  border: 2px solid white;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.other-photos {
  margin-top: 2rem;
  text-align: center;
}

.other-photos h4 {
  color: var(--color-text-muted);
  margin-bottom: 1rem;
}

.other-photos__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.other-photo {
  aspect-ratio: 1;
  border-radius: 0.5rem;
  overflow: hidden;
}

.other-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.photo-edit-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  border-radius: 50%;
}

.artist-photo:hover .photo-edit-overlay {
  opacity: 1;
}

.photo-edit-content {
  color: white;
  font-size: var(--step--1);
  text-align: center;
  padding: 0.5em;
}

.photo-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.photo-input:disabled {
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  border: 2px solid white;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.dialog-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.photo-options {
  background-color: var(--color-background);
  border-radius: 1rem;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  box-shadow: var(--shadow);
}

.photo-options h3 {
  margin-bottom: 1.5rem;
  text-align: center;
}

.photo-options h4 {
  color: var(--color-text-muted);
  margin-bottom: 1rem;
}

.previous-photos {
  margin-bottom: 2rem;
}

.previous-photos__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
}

.previous-photo {
  aspect-ratio: 1;
  border-radius: 50%;
  overflow: hidden;
  padding: 0;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.previous-photo:hover {
  border-color: var(--color-accent);
  transform: scale(1.05);
}

.previous-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5em 1em;
  border-radius: 0.5em;
  background-color: var(--color-accent);
  color: var(--color-background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-button:hover {
  filter: brightness(1.1);
}

.file-input {
  display: none;
}

.cancel-button {
  width: 100%;
  padding: 0.5em;
  border: none;
  border-radius: 0.5em;
  background-color: var(--color-background-mute);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: var(--color-background-soft);
}

.act-instruments {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  justify-content: center;
}

.instrument-tag {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  background: none;
  padding: 0;
}

.instrument-tag:not(:last-child)::after {
  content: "•";
  margin-left: 0.2rem;
  color: var(--color-text-muted);
}
</style>
