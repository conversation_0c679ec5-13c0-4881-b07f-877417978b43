<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { collection, addDoc } from 'firebase/firestore'
import { useFirebase } from '@/composables/useFirebase'
import { Artist } from '@/models/Artist'

const router = useRouter()
const { db } = useFirebase()
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const photoFile = ref<File | null>(null)

const form = ref<Partial<Artist>>({
  firstName: '',
  lastName: '',
  stageName: '',
  instruments: [''],
  photo: {
    url: '',
    publicId: ''
  }
})

const photoPreview = ref<string | null>(null)

const handlePhotoChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    photoFile.value = file  // Store the file
    form.value.photo = { url: '', publicId: '' }
    photoPreview.value = URL.createObjectURL(file)
  }
}

const computedStageName = computed(() => {
  return [form.value.firstName, form.value.lastName].join(' ').trim() || ''
})

const computedInstruments = computed(() => {
  return (form.value?.instruments?.join(',').trim() || '')
})

const uploadToCloudinary = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('upload_preset', 'artist-photos')

  try {
    const response = await fetch(
      'https://api.cloudinary.com/v1_1/dave-collison/image/upload',
      {
        method: 'POST',
        body: formData
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Upload error details:', errorData)
      throw new Error(`Upload failed: ${errorData.error?.message || 'Unknown error'}`)
    }

    const data = await response.json()
    return data.secure_url
  } catch (error) {
    console.error('Cloudinary upload error:', error)
    throw error
  }
}

const handleSubmit = async () => {
  isSubmitting.value = true
  submitError.value = null

  try {
    let photoUrl = null

    if (photoFile.value) {
      photoUrl = await uploadToCloudinary(photoFile.value)
    }

    const artistData = {
      firstName: form.value.firstName,
      lastName: form.value.lastName,
      stageName: form.value.stageName || computedStageName.value,
      instruments: computedInstruments.value,
      photoUrl,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await addDoc(collection(db, 'artists'), artistData)
    router.push('/artists')
  } catch (err) {
    console.error('Error adding artist:', err)
    submitError.value = (err as Error).message
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <header class="page-header">
    <RouterLink to="/artists" class="back-button">
      ← Artists
    </RouterLink>
    <h1>Add New Artist</h1>
  </header>

  <form @submit.prevent="handleSubmit" class="artist-form">
    <div class="form-group photo-upload">
      <label for="photo">Profile Photo</label>
      <div class="photo-preview" :class="{ 'has-photo': photoPreview }">
        <img v-if="photoPreview" :src="photoPreview" alt="Photo preview" />
        <span v-else class="photo-placeholder">Click to add photo</span>
        <input type="file" id="photo" accept="image/*" @change="handlePhotoChange" class="photo-input" />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="firstName">First Name</label>
        <input type="text" id="firstName" v-model="form.firstName" required />
      </div>

      <div class="form-group">
        <label for="lastName">Last Name</label>
        <input type="text" id="lastName" v-model="form.lastName" required />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="stageName">Stage Name (optional)</label>
        <input type="text" id="stageName" v-model="form.stageName" :placeholder="computedStageName" />
      </div>

      <div class="form-group">
        <label for="instruments">Instruments (comma-separated)</label>
        <input type="text" id="instruments" v-model="form.instruments" placeholder="e.g. guitar, vocals, piano"
          required />
      </div>
    </div>

    <div class="form-actions">
      <RouterLink to="/artists" class="cancel-button">
        Cancel
      </RouterLink>
      <BaseButton type="submit" class="submit-button" :disabled="isSubmitting">
        <span v-if="isSubmitting" class="loading-spinner"></span>
        {{ isSubmitting ? 'Adding Artist...' : 'Add Artist' }}
      </BaseButton>
    </div>

    <p v-if="submitError" class="error-message">
      {{ submitError }}
    </p>
  </form>
</template>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.back-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  text-decoration: none;
  color: var(--color-accent);
  background-color: var(--color-background-mute);
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--color-accent);
  color: var(--color-background);
}

.artist-form {
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.photo-upload {
  text-align: center;
}

.photo-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 2px dashed var(--color-border);
  margin: 1rem auto;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: var(--color-background-soft);
  transition: all 0.2s ease;
}

.photo-preview:hover {
  border-color: var(--color-accent);
}

.photo-preview.has-photo {
  border-style: solid;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-placeholder {
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.photo-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
}

input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-background);
  color: var(--color-text);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.cancel-button,
.submit-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: var(--step-0);
}

.cancel-button {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.submit-button {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid var(--color-background);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  color: rgb(239, 68, 68);
  font-size: var(--step--1);
  margin-top: 1rem;
  text-align: center;
}
</style>
