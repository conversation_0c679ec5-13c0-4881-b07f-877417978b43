import { collection, addDoc, serverTimestamp } from 'firebase/firestore'
import { db } from '../firebase/config'

const addArtists = async () => {
  const artists = [
    {
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      stageName: "<PERSON> the Keys",
      instruments: ["piano", "keyboards", "vocals"],
      actIds: ["human-jukebox", "orbison-project"],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      stageName: "Sarah Strings",
      instruments: ["guitar", "violin", "vocals"],
      actIds: ["trembling-wilburys", "human-jukebox"],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      firstName: "<PERSON>",
      lastName: "Thompson",
      stageName: "Dave T",
      instruments: ["drums", "percussion", "vocals"],
      actIds: ["daves-roy-orbison", "trembling-wilburys"],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
  ]

  try {
    for (const artist of artists) {
      await addDoc(collection(db, 'artists'), artist)
      console.log(`Added artist: ${artist.stageName}`)
    }
    console.log('All artists added successfully!')
  } catch (error) {
    console.error('Error adding artists:', error)
  }
}

addArtists()
