<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const countdown = ref(10)
let countdownInterval: ReturnType<typeof setInterval> | undefined

onMounted(() => {
  countdownInterval = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      router.back()
    }
  }, 1000)
})

onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
})
</script>

<template>
  <div class="deleted-event-dialog">
    <div class="deleted-event-content">
      <h2>Event Deleted</h2>
      <p>This event has been deleted.</p>
      <p>Returning to previous page in {{ countdown }} seconds...</p>
      <BaseButton @click="router.back()">Return Now</BaseButton>
    </div>
  </div>
</template>

<style scoped>
.deleted-event-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.deleted-event-content {
  background: var(--color-background-soft);
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 5px 10px -5px rgba(0, 0, 0, 0.2);
}

.deleted-event-content h2 {
  color: var(--color-text-danger);
  margin: 0 0 1rem;
}

.deleted-event-content p {
  margin: 0.5rem 0;
}

.button {
  margin-top: 1.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background: var(--color-accent);
  color: var(--color-background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:hover {
  filter: brightness(1.1);
}
</style>
