<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useEvents } from '@/composables/useEvents'
import { Calendar, Edit, X } from 'lucide-vue-next'

const { getEventsDateRange } = useEvents()

type Props = {
  fromDate: Date | null
  toDate?: Date | null
  defaultFromDate: Date
  showSummary?: boolean
  showYearSelector?: boolean
  availableYears?: number[] | null
  selectedYear?: string
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  toDate: null,
  showSummary: true,
  showYearSelector: false,
  availableYears: null,
  selectedYear: ''
})

const emit = defineEmits<{
  (e: 'update:fromDate', value: Date | null): void
  (e: 'update:toDate', value: Date | null): void
  (e: 'update:selectedYear', value: string): void
}>()

// Check if dates span a full year
const isFullYear = computed(() => {
  if (!props.toDate) return false

  const fromDate = props.fromDate || props.defaultFromDate
  const toDate = props.toDate

  return fromDate.getMonth() === 0 &&
    fromDate.getDate() === 1 &&
    toDate.getMonth() === 11 &&
    toDate.getDate() === 31 &&
    fromDate.getFullYear() === toDate.getFullYear()
})

// Watch date changes to update year selector
watch([() => props.fromDate, () => props.toDate], () => {
  if (!isFullYear.value && localSelectedYear.value) {
    emit('update:selectedYear', '')
  }
})

const yearRange = ref<number[]>([])

// Watch fromDate to clear toDate if fromDate is later
watch(() => props.fromDate, (newFromDate) => {
  if (props.toDate && newFromDate && newFromDate > props.toDate) {
    emit('update:toDate', null)
  }
})

// Local ref for selectedYear
const localSelectedYear = computed({
  get: () => props.selectedYear,
  set: (value: string) => {
    emit('update:selectedYear', value)
  }
})

// Dialog state
const showDateDialog = ref(false)

// Store previous date state when opening dialog
const previousFromDate = ref<Date | null>(null)
const previousToDate = ref<Date | null>(null)

// Watch dialog opening to store previous state
watch(showDateDialog, (isOpen) => {
  if (isOpen) {
    previousFromDate.value = props.fromDate
    previousToDate.value = props.toDate
  }
})

// Watch dialog opening/closing
watch(showDateDialog, (isOpen) => {
  // Remove the automatic date setting when closing dialog
  // No action needed when dialog closes without selection
})

// Computed property for year select options
const yearSelectOptions = computed(() => {
  if (!yearRange.value.length) {
    return [{ value: '', label: 'Select year...' }]
  }


  const firstYear = yearRange.value[0]
  const lastYear = yearRange.value[yearRange.value.length - 1]

  const years = Array.from(
    { length: lastYear - firstYear + 1 },
    (_, i) => firstYear + i
  )

  return [
    { value: '', label: 'Select year...' },
    ...years.map(year => ({
      value: year.toString(),
      label: year.toString()
    }))
  ]
})

// Watch for year selection changes
watch(() => localSelectedYear.value, (newYear) => {
  if (newYear) {
    const year = parseInt(newYear)
    const start = new Date(year, 0, 1) // January 1st
    const end = new Date(year, 11, 31)
    end.setHours(23, 59, 59, 999) // Set to end of day
    emit('update:fromDate', start)
    emit('update:toDate', end)
  }
})

// Local date state for v-model binding
const startDateString = computed({
  get: () => props.fromDate ? formatDateForInput(props.fromDate) : '',
  set: (value: string) => {
    if (value) {
      const newDate = new Date(value + 'T00:00:00')
      emit('update:fromDate', newDate)
      // If we have a toDate and the new fromDate is after it, clear toDate
      if (props.toDate && newDate > props.toDate) {
        emit('update:toDate', null)
      }
    } else {
      emit('update:fromDate', null)
    }
  }
})

const endDateString = computed({
  get: () => props.toDate ? formatDateForInput(props.toDate) : '',
  set: (value: string) => {
    if (!value) {
      emit('update:toDate', null)
      return
    }
    // Set the time to end of day (23:59:59)
    const date = new Date(value + 'T00:00:00')
    date.setHours(23, 59, 59, 999)
    emit('update:toDate', date)
  }
})

// Check if a date is today
const isFromDateToday = computed(() => {
  const today = new Date()
  const dateToCheck = props.fromDate || props.defaultFromDate
  return dateToCheck.getFullYear() === today.getFullYear() &&
    dateToCheck.getMonth() === today.getMonth() &&
    dateToCheck.getDate() === today.getDate()
})

// Helper functions
function formatDateForInput(date: Date): string {
  return date.toISOString().split('T')[0]
}

function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}

function clearDates() {
  emit('update:fromDate', null)
  emit('update:toDate', null)
  emit('update:selectedYear', '')
  showDateDialog.value = false
}

function setFromDateToday() {
  emit('update:fromDate', new Date())
}

function clearFromDate() {
  emit('update:fromDate', null)
}

function setQuickDateRange(days: number) {
  const toDate = addDays(props.fromDate || props.defaultFromDate, days)
  toDate.setHours(23, 59, 59, 999)
  emit('update:toDate', toDate)
}

function setDateRangeFromToday(days: number) {
  const fromDate = new Date()
  const toDate = addDays(fromDate, days)
  toDate.setHours(23, 59, 59, 999)
  emit('update:fromDate', fromDate)
  emit('update:toDate', toDate)
  showDateDialog.value = false
}

function cancel() {
  emit('update:fromDate', previousFromDate.value)
  emit('update:toDate', previousToDate.value)
  showDateDialog.value = false
}

// Format date range for display
const dateRangeText = computed((): string => {
  // If no dates are selected, show default text
  if (!props.fromDate && !props.toDate) {
    return 'Showing all upcoming'
  }

  // If only fromDate is selected
  if (props.fromDate && !props.toDate) {
    return `From ${props.fromDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })}`
  }

  // If both dates are selected and it's a full year
  if (isFullYear.value && props.fromDate) {
    return `Year of ${props.fromDate.getFullYear()}`
  }

  // If both dates are selected
  if (props.fromDate && props.toDate) {
    const fromText = props.fromDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })

    const toText = props.toDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })

    return `${fromText} - ${toText}`
  }

  return 'Showing all upcoming'
})

// Add watch for fromDate to clear toDate when fromDate is cleared
watch(() => props.fromDate, (newFromDate) => {
  // Clear toDate if fromDate is cleared or if fromDate is later than toDate
  if (!newFromDate || (props.toDate && newFromDate > props.toDate)) {
    emit('update:toDate', null)
  }
})

onMounted(async () => {
  if (props.showYearSelector || props.availableYears?.length) {
    if (props.availableYears?.length) {
      yearRange.value = props.availableYears
      return
    }

    const dateRange = await getEventsDateRange()
    if (dateRange) {
      yearRange.value = dateRange.years()
    }
  }
})
</script>

<template>
  <div class="date-range-selector">
    <div class="selectors-container">
      <div class="selectors-row">
        <BaseSelect v-if="showYearSelector || yearRange.length > 0" v-model="localSelectedYear"
          :options="yearSelectOptions" placeholder="Select a year" size="default" type="ghost"
          :id="id ? `${id}-year-select` : 'year-select'" />
        <div class="date-range-controls">
          <template v-if="fromDate || toDate">
            <div class="date-range-actions">
              <BaseButton @click="showDateDialog = true" title="Edit date range" size="compact" purpose="primary">
                <Edit :size="16" />
              </BaseButton>
              <BaseButton @click="clearDates" title="Clear date range" size="compact" purpose="danger">
                <X :size="16" />
              </BaseButton>
            </div>
          </template>
          <BaseButton v-else size="compact" @click="showDateDialog = true">
            <Calendar :size="16" />
            Select date range
          </BaseButton>
        </div>
      </div>
      <div v-if="showSummary" class="date-range-row">
        <div class="date-range-text">
          <Calendar class="icon" :size="16" />
          <span>{{ dateRangeText }}</span>
        </div>
      </div>
    </div>

    <!-- Date Range Dialog -->
    <BaseDialog v-model="showDateDialog" title="Select Date Range">
      <div class="date-inputs">
        <div v-show="!fromDate && !toDate" class="quick-select-buttons" style="margin-bottom: 1rem;">
          <BaseButton size="compact" purpose="secondary" @click="setDateRangeFromToday(7)" class="quick-select-btn">
            7 days from today
          </BaseButton>
          <BaseButton size="compact" purpose="secondary" @click="setDateRangeFromToday(28)" class="quick-select-btn">
            4 weeks from today
          </BaseButton>
        </div>

        <div class="date-input-group">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <label for="start-date">From</label>
            <div v-if="!fromDate || !isFromDateToday" class="quick-select-buttons">
              <BaseButton size="compact" purpose="secondary" @click="setFromDateToday" class="quick-select-btn">
                Today
              </BaseButton>
            </div>
          </div>
          <div class="date-input-wrapper">
            <input type="date" id="start-date" v-model="startDateString" :max="endDateString || undefined"
              :placeholder="formatDateForInput(props.fromDate || props.defaultFromDate)">
            <BaseButton v-if="fromDate" @click="clearFromDate" size="tiny" variant="ghost" pill title="Clear from date">
              <X :size="12" />
            </BaseButton>
          </div>
        </div>

        <div class="date-input-group" v-if="fromDate">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <label for="end-date">To</label>
            <div class="quick-select-buttons">
              <BaseButton size="compact" purpose="secondary" @click="setQuickDateRange(7)" class="quick-select-btn">
                7 days
              </BaseButton>
              <BaseButton size="compact" purpose="secondary" @click="setQuickDateRange(28)" class="quick-select-btn">
                4 weeks
              </BaseButton>
            </div>
          </div>
          <div class="date-input-wrapper">
            <input type="date" id="end-date" v-model="endDateString" :min="startDateString || undefined">
            <BaseButton v-if="toDate" @click="() => emit('update:toDate', null)" size="tiny" variant="ghost" pill
              title="Clear to date">
              <X :size="12" />
            </BaseButton>
          </div>
        </div>
      </div>
      <template #actions>
        <BaseButton class="clear-dates-btn" @click="clearDates" v-if="fromDate || toDate" size="compact"
          purpose="secondary">
          Show All Upcoming
        </BaseButton>
        <BaseButton @click="cancel" size="compact" purpose="secondary">
          Cancel
        </BaseButton>
        <BaseButton class="close-dialog-btn" @click="showDateDialog = false" size="compact" purpose="primary">
          Done
        </BaseButton>
      </template>
    </BaseDialog>
  </div>
</template>

<style scoped>
.date-range-selector {
  width: 100%;
}

.selectors-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.selectors-row {
  display: flex;
  gap: var(--space-s);
  align-items: center;
}

.selectors-row :deep(.select-wrapper) {
  flex: 1;
  min-width: 150px;
}

.date-range-controls {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-xs);
}

.date-range-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: var(--space-xs);
  border-top: 1px solid var(--color-border-soft);
}

.date-range-text {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text);
  font-size: var(--step--1);
}

.icon {
  color: var(--color-text-light);
}

.date-range-actions {
  display: flex;
  gap: var(--space-xs);
  align-items: center;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  padding: var(--space-xs) 0;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.date-input-group label {
  font-size: var(--step--1);
  font-weight: 500;
  color: var(--color-text);
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.date-input-wrapper input {
  flex: 1;
  padding: var(--space-s);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  color: var(--color-text);
  font-size: var(--step-0);
  transition: all var(--transition-out);
}

.date-input-wrapper input:hover {
  border-color: var(--color-border-hover);
}

.date-input-wrapper input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 1px var(--color-accent);
}

.quick-select-buttons {
  display: flex;
  gap: var(--space-s);
  justify-content: center;
}

.date-input-wrapper input.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
