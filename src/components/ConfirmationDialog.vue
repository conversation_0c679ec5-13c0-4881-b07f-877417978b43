<script setup lang="ts">
import { useConfirmationStore } from '@/stores/confirmationStore'
import { storeToRefs } from 'pinia'
import { onMounted, onUnmounted, ref, watch, computed, nextTick } from 'vue'
import { X } from 'lucide-vue-next'

const confirmationStore = useConfirmationStore()
const { isOpen, title, message, confirmText, cancelText, type } = storeToRefs(confirmationStore)

const dialogRef = ref<HTMLDialogElement | null>(null)

function handleEscape(e: KeyboardEvent) {
  if (e.key === 'Escape' && isOpen.value) {
    confirmationStore.handleCancel()
  }
}

// Handle clicks on the backdrop
function handleBackdropClick(e: MouseEvent) {
  const rect = dialogRef.value?.getBoundingClientRect()
  if (rect) {
    const isInDialog = (
      e.clientX >= rect.left &&
      e.clientX <= rect.right &&
      e.clientY >= rect.top &&
      e.clientY <= rect.bottom
    )
    if (!isInDialog) {
      confirmationStore.handleCancel()
    }
  }
}

// Create a computed property for the button type
const buttonType = computed(() => {
  return type.value === 'warning' ? 'danger' : 'primary'
})

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})

// Watch for changes to isOpen and handle dialog accordingly
watch(() => isOpen.value, async (value) => {
  if (value) {
    // Wait for the dialog to be added to the DOM before showing it
    await nextTick()
    if (dialogRef.value) {
      dialogRef.value.showModal()
    }
  }
})
</script>

<template>
  <Teleport to="body">
    <dialog v-if="isOpen" ref="dialogRef" class="confirmation-dialog" @click="handleBackdropClick">
      <div class="dialog-content" @click.stop>
        <header class="dialog-header">
          <h2 class="dialog-title">{{ title }}</h2>
          <button class="close-button" @click="confirmationStore.handleCancel">
            <X :size="20" />
          </button>
        </header>

        <div class="dialog-body">
          <p>{{ message }}</p>
        </div>

        <footer class="dialog-footer">
          <BaseButton @click="confirmationStore.handleCancel" purpose="secondary">
            {{ cancelText }}
          </BaseButton>
          <BaseButton @click="confirmationStore.handleConfirm" :purpose="buttonType">
            {{ confirmText }}
          </BaseButton>
        </footer>
      </div>
    </dialog>
  </Teleport>
</template>

<style scoped>
.confirmation-dialog {
  place-self: center;
  padding: 0;
  border: none;
  border-radius: var(--radius-l);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
  max-width: 90%;
  width: 450px;
  background: transparent;
}

.confirmation-dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.dialog-content {
  background: var(--color-background);
  border-radius: var(--radius-l);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: var(--space-m);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-title {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-heading);
}

.close-button {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  transition: color 0.2s ease;
}

.close-button:hover {
  color: var(--color-text);
}

.dialog-body {
  padding: var(--space-m);
  flex-grow: 1;
}

.dialog-footer {
  padding: var(--space-m);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-s);
}
</style>
