<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import type { FunctionalComponent } from 'vue'
import type { LucideProps } from 'lucide-vue-next'

type Props = {
  title: string
  icon?: string
  variant?: 'default' | 'outline' | 'shadow'
}

const props = withDefaults(defineProps<Props>(), {
  icon: '',
  variant: 'default'
})

const LucideIcon = ref<FunctionalComponent<LucideProps> | null>(null)

watchEffect(async () => {
  if (!props.icon) {
    LucideIcon.value = null
    return
  }

  try {
    // Convert kebab-case to PascalCase
    const iconName = props.icon
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('')

    const module = await import('lucide-vue-next')
    LucideIcon.value = (module as any)[iconName] || null
  } catch (error) {
    console.error(`Failed to load icon: ${props.icon}`, error)
    LucideIcon.value = null
  }
})
</script>

<template>
  <BaseCard :variant="variant" class="dashboard-card">
    <template #header>
      <div class="dashboard-card__header">
        <div v-if="LucideIcon" class="dashboard-card__icon">
          <component :is="LucideIcon" />
        </div>
        <h3 class="dashboard-card__title">{{ title }}</h3>
      </div>
    </template>

    <div class="dashboard-card__content" v-if="$slots.default">
      <slot></slot>
    </div>

    <template v-if="$slots.footer" #footer>
      <slot name="footer"></slot>
    </template>
  </BaseCard>
</template>

<style scoped>
.dashboard-card {
  container-type: inline-size;
}

.dashboard-card__header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.6em;
  height: 1.6em;
  background-color: var(--color-background-mute);
  border-radius: 0.3em;
}

.dashboard-card__title {
  font-size: var(--step-1);
  color: var(--color-heading);
  line-height: 1.2;
}

.dashboard-card__content {
  display: grid;
  gap: 1rem;
}
</style>
