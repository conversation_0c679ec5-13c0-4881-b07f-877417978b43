<script setup lang="ts">
import { ref } from 'vue'
import { X } from 'lucide-vue-next'

type Note = {
  content: string
  type: 'general' | 'technical' | 'financial'
  visibility: 'private' | 'team' | 'public'
}

const props = defineProps({
  eventId: {
    type: String,
    required: false,
    default: null
  }
})

const emit = defineEmits(['close', 'save'])

const note = ref<Note>({
  content: '',
  type: 'general',
  visibility: 'private'
})

const isLoading = ref(false)
const error = ref<string | null>(null)

const saveNote = async () => {
  try {
    isLoading.value = true
    error.value = null

    const noteData = {
      ...note.value,
      eventId: props.eventId,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Implement your own save logic here
    emit('save')
  } catch (e) {
    console.error('Error saving note:', e)
    error.value = 'Failed to save note'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="notes-form">
    <header class="notes-form__header">
      <h2>Add Note</h2>
      <BaseButton icon @click="$emit('close')" title="Close">
        <X />
      </BaseButton>
    </header>

    <form @submit.prevent="saveNote">
      <div class="form-group">
        <label for="note-type">Type</label>
        <select id="note-type" v-model="note.type">
          <option value="general">General</option>
          <option value="technical">Technical</option>
          <option value="financial">Financial</option>
        </select>
      </div>

      <div class="form-group">
        <label for="note-visibility">Visibility</label>
        <select id="note-visibility" v-model="note.visibility">
          <option value="private">Private</option>
          <option value="team">Team</option>
          <option value="public">Public</option>
        </select>
      </div>

      <div class="form-group">
        <label for="note-content">Note</label>
        <textarea id="note-content" v-model="note.content" rows="6" placeholder="Enter your note..."
          required></textarea>
      </div>

      <div class="form-actions">
        <BaseButton type="button" purpose="secondary" @click="$emit('close')">
          Cancel
        </BaseButton>
        <BaseButton type="submit" purpose="primary" :disabled="isLoading">
          {{ isLoading ? 'Saving...' : 'Save Note' }}
        </BaseButton>
      </div>

      <p v-if="error" class="error-message">{{ error }}</p>
    </form>
  </div>
</template>

<style scoped>
.notes-form {
  background: var(--color-background);
  border-radius: 1rem;
  padding: 2rem;
  width: 90%;
  max-width: 600px;
  box-shadow: var(--shadow);
}

.notes-form__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notes-form__header h2 {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-accent);
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: var(--color-background-mute);
  color: var(--color-text);
  font-size: var(--step-2);
  line-height: 1;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: var(--color-accent);
  color: var(--color-background);
}

form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.form-group label {
  font-weight: 500;
  color: var(--color-text);
  font-size: var(--step-0);
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step-0);
}

.form-group select {
  height: 3rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 0.5rem;
}

.button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  cursor: pointer;
  font-size: var(--step-0);
  transition: all 0.2s ease;
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.button--primary:hover {
  filter: brightness(1.1);
}

.button--secondary {
  background: var(--color-background-mute);
  color: var(--color-text);
}

.button--secondary:hover {
  background: var(--color-background-soft);
}

.error-message {
  color: var(--color-text-danger);
  text-align: center;
  margin-top: 1rem;
  font-size: var(--step--1);
}
</style>
