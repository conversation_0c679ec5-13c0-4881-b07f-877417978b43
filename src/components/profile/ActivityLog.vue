<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useActivityLog } from '@/composables/useActivityLog'
import { ActivityIcon, FilterIcon } from 'lucide-vue-next'
import type { ActivityType, FetchOptions } from '@/composables/useActivityLog'
import type { UserActivity } from '@/types/user'

const {
  isLoading,
  error,
  fetchActivities,
  getActivityLabel,
  ACTIVITY_TYPES
} = useActivityLog()

const activities = ref<Array<UserActivity>>([])
const selectedType = ref<ActivityType | ''>('')
const startDate = ref<string>('')
const endDate = ref<string>('')
const showFilters = ref(false)

async function loadActivities() {
  try {
    const options: FetchOptions = {
      limit: 50
    }

    if (selectedType) {
      options.activityType = selectedType.value as ActivityType
    }

    if (startDate.value) {
      options.startDate = new Date(startDate.value)
    }

    if (endDate.value) {
      options.endDate = new Date(endDate.value)
    }

    activities.value = await fetchActivities(options)
  } catch (err) {
    console.error('Error loading activities:', err)
  }
}

function formatTimestamp(timestamp: { seconds: number; nanoseconds: number }) {
  const date = new Date(timestamp.seconds * 1000)
  return new Intl.DateTimeFormat('en-US', {
    dateStyle: 'medium',
    timeStyle: 'short'
  }).format(date)
}

function clearFilters() {
  selectedType.value = ''
  startDate.value = ''
  endDate.value = ''
  loadActivities()
}

onMounted(() => {
  loadActivities()
})
</script>

<template>
  <BaseSection title="Activity Log">
    <div class="activity-log">
      <div class="activity-header">
        <BaseButton variant="ghost" purpose="secondary" @click="showFilters = !showFilters"
          :class="{ active: showFilters }">
          <FilterIcon :size="16" />
          Filters
        </BaseButton>
      </div>

      <div v-if="showFilters" class="filters">
        <div class="filter-group">
          <BaseSelect v-model="selectedType" label="Activity Type" :options="Object.keys(ACTIVITY_TYPES)"
            @change="loadActivities" id="activity-type-select" />
        </div>

        <div class="filter-group">
          <BaseInput v-model="startDate" type="date" label="From Date" @change="loadActivities" />
          <BaseInput v-model="endDate" type="date" label="To Date" @change="loadActivities" />
        </div>

        <BaseButton variant="ghost" purpose="secondary" @click="clearFilters">
          Clear Filters
        </BaseButton>
      </div>

      <div v-if="isLoading" class="loading">
        <LoadingSpinner />
        Loading activity log...
      </div>

      <div v-else-if="error" class="error" role="alert">
        {{ error }}
      </div>

      <div v-else-if="activities.length === 0" class="empty-state">
        <ActivityIcon :size="32" />
        <p>No activity recorded yet</p>
      </div>

      <div v-else class="activities-list">
        <div v-for="activity in activities" :key="activity.id" class="activity-item">
          <div class="activity-icon">
            <ActivityIcon :size="16" />
          </div>

          <div class="activity-content">
            <p class="activity-text">
              {{ getActivityLabel(activity.action) }}
              <span v-if="activity.details?.newEmail">
                to {{ activity.details.newEmail }}
              </span>
            </p>

            <div class="activity-meta">
              <time :datetime="formatTimestamp(activity.timestamp)">
                {{ formatTimestamp(activity.timestamp) }}
              </time>
              <span v-if="activity.ip" class="activity-ip">
                from {{ activity.ip }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.activity-log {
  min-height: 200px;
}

.activity-header {
  margin-bottom: var(--space-m);
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-m);
  margin-bottom: var(--space-l);
  padding: var(--space-m);
  background: var(--color-surface);
  border-radius: var(--radius);
}

.filter-group {
  display: flex;
  gap: var(--space-m);
  flex: 1;
  min-width: 200px;
}

.loading,
.error,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-s);
  min-height: 200px;
  color: var(--color-text-soft);
}

.error {
  color: var(--color-danger);
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.activity-item {
  display: flex;
  gap: var(--space-s);
  padding: var(--space-s);
  background: var(--color-surface);
  border-radius: var(--radius);
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background: var(--color-surface-hover);
}

.activity-icon {
  color: var(--color-text-soft);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-surface-hover);
  border-radius: var(--radius);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  margin: 0;
  color: var(--color-text);
  font-weight: 500;
}

.activity-meta {
  display: flex;
  gap: var(--space-s);
  margin-top: var(--space-2xs);
  font-size: var(--step--1);
  color: var(--color-text-soft);
}

.activity-ip {
  color: var(--color-text-muted);
}

.active {
  background: var(--color-surface-hover);
}

@media (max-width: 500px) {
  .activity-meta {
    flex-direction: column;
    gap: var(--space-2xs);
  }

  .filter-group {
    flex-direction: column;
  }
}
</style>
