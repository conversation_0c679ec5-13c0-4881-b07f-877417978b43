<script setup lang="ts">
import { ref } from 'vue'
import { useProfile } from '@/composables/useProfile'
import { useRouter } from 'vue-router'
import { DownloadIcon, TrashIcon } from 'lucide-vue-next'

const { exportUserData, deleteAccount } = useProfile()
const router = useRouter()

const isExporting = ref(false)
const isDeletingAccount = ref(false)
const confirmPassword = ref('')
const error = ref<string | null>(null)

async function handleExportData() {
  try {
    isExporting.value = true
    error.value = null

    const blob = await exportUserData()
    if (!blob) {
      error.value = 'Failed to export data'
      return
    }

    // Create download link
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `account-data-${new Date().toISOString()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to export data'
  } finally {
    isExporting.value = false
  }
}

async function handleDeleteAccount() {
  if (!confirmPassword.value) {
    error.value = 'Please enter your password to confirm account deletion'
    return
  }

  try {
    const success = await deleteAccount(confirmPassword.value)
    if (success) {
      router.push('/login')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to delete account'
  }
}
</script>

<template>
  <BaseSection title="Account Management">
    <div class="account-management">
      <!-- Data Export -->
      <BaseCard>
        <div class="management-item">
          <div class="item-header">
            <DownloadIcon :size="20" />
            <h3>Export Data</h3>
          </div>

          <div class="item-content">
            <p>Download a copy of your personal data</p>
            <BaseButton type="secondary" :loading="isExporting" @click="handleExportData">
              Export Data
            </BaseButton>
          </div>
        </div>
      </BaseCard>

      <!-- Account Deletion -->
      <BaseCard class="danger-zone">
        <div class="management-item">
          <div class="item-header">
            <TrashIcon :size="20" />
            <h3>Delete Account</h3>
          </div>

          <div class="item-content">
            <p class="warning-text">
              This action cannot be undone. All your data will be permanently deleted.
            </p>

            <template v-if="!isDeletingAccount">
              <BaseButton type="danger" @click="isDeletingAccount = true">
                Delete Account
              </BaseButton>
            </template>

            <form v-else @submit.prevent="handleDeleteAccount" class="delete-form">
              <BaseInput v-model="confirmPassword" type="password" label="Confirm your password" required />

              <p v-if="error" class="error-message" role="alert">
                {{ error }}
              </p>

              <div class="form-actions">
                <BaseButton type="secondary" @click="isDeletingAccount = false">
                  Cancel
                </BaseButton>
                <BaseButton type="danger" native-type="submit">
                  Confirm Deletion
                </BaseButton>
              </div>
            </form>
          </div>
        </div>
      </BaseCard>
    </div>
  </BaseSection>
</template>

<style scoped>
.account-management {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.management-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.item-header {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-heading);
}

.item-header h3 {
  margin: 0;
  font-size: var(--step-1);
  font-weight: 600;
}

.item-content {
  color: var(--color-text);
}

.danger-zone {
  border: 1px solid var(--color-danger-soft);

  .item-header {
    color: var(--color-danger);
  }
}

.warning-text {
  color: var(--color-danger);
  font-weight: 500;
  margin-bottom: var(--space-m);
}

.delete-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  max-width: 400px;
  margin-top: var(--space-m);
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  justify-content: flex-end;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
}
</style>
