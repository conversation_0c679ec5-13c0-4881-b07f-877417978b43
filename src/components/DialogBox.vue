<script setup lang="ts">
import { useMessageStore } from '../stores/messageStore'
import { storeToRefs } from 'pinia'
import type { Ref } from 'vue'

type Dialog = {
  title: string
  message: string
  type: 'alert' | 'confirm'
  confirmText: string
  cancelText?: string
  onConfirm?: () => Promise<void> | void
  onCancel?: () => void
}

const messageStore = useMessageStore()
const { currentDialog } = storeToRefs(messageStore) as unknown as {
  currentDialog: Ref<Dialog | null>
}

const handleConfirm = async () => {
  const dialog = currentDialog.value
  messageStore.closeDialog() // Close dialog first
  if (dialog?.onConfirm) {
    await dialog.onConfirm() // Then execute callback
  }
}

const handleCancel = () => {
  const dialog = currentDialog.value
  messageStore.closeDialog() // Close dialog first
  if (dialog?.onCancel) {
    dialog.onCancel() // Then execute callback
  }
}

const getButtonPurpose = (type: 'alert' | 'confirm', isCancel = false): 'primary' | 'secondary' | 'danger' => {
  if (isCancel) return 'secondary'
  return type === 'confirm' ? 'danger' : 'primary'
}
</script>

<template>
  <Teleport to="body">
    <div v-if="currentDialog" class="dialog-overlay" @click.self="handleCancel">
      <div class="dialog-box">
        <h3>{{ currentDialog.title }}</h3>
        <p>{{ currentDialog.message }}</p>
        <div class="dialog-actions">
          <BaseButton v-if="currentDialog.type === 'confirm'" :purpose="getButtonPurpose(currentDialog.type, true)"
            variant="solid" @click="handleCancel">
            {{ currentDialog.cancelText || 'Cancel' }}
          </BaseButton>
          <BaseButton :purpose="getButtonPurpose(currentDialog.type)" variant="solid" @click="handleConfirm">
            {{ currentDialog.confirmText }}
          </BaseButton>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.dialog-box {
  background: var(--color-background);
  padding: 2rem;
  border-radius: 1rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.dialog-box h3 {
  color: var(--color-text-danger);
  margin: 0 0 1rem;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.button--secondary {
  background: var(--color-background-mute);
  color: var(--color-text);
}

.button--danger {
  background: var(--color-text-danger);
  color: var(--color-background);
  border-color: var(--color-text-danger);
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.button--danger:hover,
.button--primary:hover {
  filter: brightness(1.1);
}
</style>
