<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useUsers } from '@/composables/useUsers'
import { User } from '@/models/User'
import BaseDropdown from '../base/BaseDropdown.vue'
import BaseButton from '../base/BaseButton.vue'
import TaskNotificationDot from '../tasks/TaskNotificationDot.vue'

const { user: authUser, isLoading: authLoading, logout } = useAuth() // Get auth loading state
const { getCurrentUserData, cleanup } = useUsers()
const isMenuOpen = ref(false)
const isDropdownOpen = ref(false)
const currentUser = ref<User | null>(null)
const isLoading = ref(true)

// Computed property to determine if we're ready to render
const isReady = computed(() => !authLoading.value && !isLoading.value)

onMounted(async () => {
  isLoading.value = true
  try {
    // Wait for auth to be ready and have a user
    if (authUser.value) {
      const userData = await getCurrentUserData()
      currentUser.value = userData
    }
  } catch (error) {
    console.error('Error loading user:', error)
  } finally {
    isLoading.value = false
  }
})

// Watch for auth user changes
watch(authUser, async (newUser) => {
  if (newUser) {
    isLoading.value = true
    try {
      const userData = await getCurrentUserData()
      currentUser.value = userData
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      isLoading.value = false
    }
  } else {
    currentUser.value = null
  }
})

onUnmounted(() => {
  cleanup() // User cleanup
})

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value
}

function closeMenu() {
  isMenuOpen.value = false
}

function handleLogout() {
  logout()
  isDropdownOpen.value = false
}

const isViewDropdownOpen = ref(false)
const isAddDropdownOpen = ref(false)

watch([isViewDropdownOpen, isAddDropdownOpen, isDropdownOpen], (newVal, oldVal) => {
  // Get index of the newly opened dropdown
  const openedIndex = newVal.findIndex((val, i) => val && !oldVal[i])

  if (openedIndex === -1) return // No new dropdown was opened

  // Close other dropdowns
  if (openedIndex !== 0) isViewDropdownOpen.value = false
  if (openedIndex !== 1) isAddDropdownOpen.value = false
  if (openedIndex !== 2) isDropdownOpen.value = false
}, { deep: true })
</script>

<template>
  <nav v-if="isReady">
    <BaseButton icon variant="ghost" class="menu-toggle" @click="toggleMenu" aria-label="Toggle menu">
      <span></span>
      <span></span>
      <span></span>
    </BaseButton>

    <div class="nav-links" :class="{ 'is-open': isMenuOpen }">
      <RouterLink :to="{ name: 'calendar' }" class="nav-link" @click="closeMenu">Calendar</RouterLink>
      <BaseDropdown v-model:isOpen="isViewDropdownOpen" icon placement="bottom" size="compact" :offset="8"
        class="nav-dropdown">
        <template #trigger>View</template>
        <RouterLink :to="{ name: 'events.index' }" class="nav-link" @click="closeMenu; isViewDropdownOpen = false">
          Events
        </RouterLink>
        <RouterLink :to="{ name: 'venues.index' }" class="nav-link" @click="closeMenu; isViewDropdownOpen = false">
          Venues
        </RouterLink>
        <RouterLink :to="{ name: 'acts.index' }" class="nav-link" @click="closeMenu; isViewDropdownOpen = false">Acts
        </RouterLink>
        <RouterLink :to="{ name: 'artists.index' }" class="nav-link" @click="closeMenu; isViewDropdownOpen = false">
          Artists
        </RouterLink>
      </BaseDropdown>
      <BaseDropdown v-model:isOpen="isAddDropdownOpen" icon placement="bottom" size="compact" :offset="8"
        class="nav-dropdown">
        <template #trigger>Add</template>
        <RouterLink :to="{ name: 'events.create' }" class="nav-link" @click="closeMenu; isAddDropdownOpen = false">
          Event
        </RouterLink>
        <RouterLink :to="{ name: 'venues.create' }" class="nav-link" @click="closeMenu; isAddDropdownOpen = false">
          Venue
        </RouterLink>
        <RouterLink :to="{ name: 'acts.create' }" class="nav-link" @click="closeMenu; isAddDropdownOpen = false">Act
        </RouterLink>
        <RouterLink :to="{ name: 'artists.create' }" class="nav-link" @click="closeMenu; isAddDropdownOpen = false">
          Artist
        </RouterLink>
      </BaseDropdown>
      <RouterLink :to="{ name: 'tasks' }" class="nav-link nav-link--with-dot" @click="closeMenu">
        Tasks&nbsp;
        <TaskNotificationDot />
      </RouterLink>

      <BaseDropdown v-if="!isLoading && currentUser" v-model:isOpen="isDropdownOpen" placement="bottom" :offset="8"
        class="user-dropdown">
        <template #trigger>
          <BaseButton variant="ghost" class="user-trigger" :aria-expanded="isDropdownOpen" aria-haspopup="true">
            <div class="user-trigger__avatar">
              <img v-if="currentUser.photoURL" :src="currentUser.photoURL" :alt="currentUser.displayName"
                class="avatar-image">
              <div v-else class="avatar-text">
                {{ currentUser.firstName?.charAt(0) }}{{ currentUser.lastName?.charAt(0) }}
              </div>
            </div>
          </BaseButton>
        </template>

        <div class="user-info">
          <p class="user-name">{{ currentUser.fullName }}</p>
          <p class="user-email">{{ currentUser.email }}</p>
        </div>
        <div class="dropdown-divider"></div>
        <RouterLink to="/profile" class="dropdown-item" @click="isDropdownOpen = false" role="menuitem">
          Profile
        </RouterLink>
        <BaseButton purpose="secondary" class="dropdown-item" @click="handleLogout" role="menuitem">
          Sign Out
        </BaseButton>
      </BaseDropdown>

      <!-- Show loading state -->
      <div v-else class="user-trigger__avatar">
        <div class="avatar-text">...</div>
      </div>
    </div>
  </nav>
  <nav v-else class="nav-loading">
    <!-- Optional loading state -->
    <div class="user-trigger__avatar">
      <div class="avatar-text">...</div>
    </div>
  </nav>
</template>

<style scoped>
nav {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  /* Align all content to the right */
  margin-left: auto;
  /* Push nav to the right */
  width: 100%;
  /* Take full width to ensure proper alignment */
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--space-2xs);
  margin-left: auto;
  /* Push nav links to the right */
}

.nav-link {
  color: var(--color-text);
  padding: var(--space-2xs) var(--space-xs);
  border-radius: var(--radius-m);
  font-size: var(--step--1);
  background: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;

  &.nav-link--with-dot {
    position: relative;
  }
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--color-accent);
  background: var(--color-background-soft);
}

.menu-toggle {
  display: none;
  width: 2rem;
  height: 2rem;
  position: relative;
  padding: 0;
  margin-left: auto;
}

.menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background: currentColor;
  margin: 5px 0;
  transition: 0.3s;
}

/* Burger animation */
.is-open~.menu-toggle span:first-child {
  transform: translateY(7px) rotate(45deg);
}

.is-open~.menu-toggle span:nth-child(2) {
  opacity: 0;
}

.is-open~.menu-toggle span:last-child {
  transform: translateY(-7px) rotate(-45deg);
}

.user-dropdown {
  margin-left: var(--space-s);
  max-width: 320px;
}

.user-trigger {
  display: flex;
  align-items: center;
  padding: var(--space-2xs);
  border-radius: var(--radius-m);
  transition: all var(--transition-fast);
}

.user-trigger__avatar:not(:empty) {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.user-trigger__name {
  font-size: var(--step--1);
  font-weight: 500;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  font-size: var(--step--1);
  font-weight: 600;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.user-info {
  padding: var(--space-s) var(--space-m);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background);
  width: 100%;
}

.user-name {
  font-weight: 600;
  font-size: var(--step--1);
  margin: 0;
  line-height: 1.4;
  white-space: nowrap;
  display: block;
}

.user-email {
  font-size: var(--step--2);
  margin: var(--space-2xs) 0 0;
  line-height: 1.4;
  white-space: nowrap;
  display: block;
}

.dropdown-divider {
  height: 1px;
  background: var(--color-border);
  margin: var(--space-2xs) 0;
  width: 100%;
}

.dropdown-item {
  width: 100%;
  text-align: left;
  border-radius: var(--radius-s);
  text-decoration: none;
  padding: var(--space-xs) var(--space-s);
  color: var(--color-text);
  display: flex;
  align-items: center;
  font-size: var(--step--1);
  line-height: 1.4;
  transition: all var(--transition-fast);
  border: none;
  background: none;
  cursor: pointer;
}

@media (max-width: 768px) {
  nav {
    position: relative;
    margin-left: 0;
    width: auto;
    /* Allow nav to size to content on mobile */
  }

  .menu-toggle {
    display: block;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    right: 0;
    flex-direction: column;
    background: var(--color-background);
    padding: var(--space-s);
    border-radius: var(--radius-m);
    border: 1px solid var(--color-border);
    gap: var(--space-2xs);
    transform: scale(0);
    transform-origin: top right;
    opacity: 0;
    transition: all var(--transition-fast);
    min-width: 200px;
    max-width: calc(100vw - 2rem);
    box-shadow: var(--shadow-md);
    margin-left: 0;
  }

  .nav-links.is-open {
    transform: scale(1);
    opacity: 1;
  }

  .nav-link {
    width: 100%;
    text-align: left;
    padding: var(--space-xs) var(--space-s);
  }

  .user-dropdown {
    margin-left: 0;
    width: 100%;
  }

  .dropdown-item {
    padding: var(--space-s);
  }

  /* Burger animation */
  .is-open~.menu-toggle span:first-child {
    transform: translateY(7px) rotate(45deg);
  }

  .is-open~.menu-toggle span:nth-child(2) {
    opacity: 0;
  }

  .is-open~.menu-toggle span:last-child {
    transform: translateY(-7px) rotate(-45deg);
  }

  .user-trigger__name {
    display: none;
    /* Hide name on mobile */
  }

  .user-trigger__avatar {
    width: 2rem;
    height: 2rem;
  }
}

.nav-loading {
  display: flex;
  justify-content: flex-end;
  padding: var(--space-xs);
}
</style>
