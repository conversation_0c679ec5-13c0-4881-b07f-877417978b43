<script setup lang="ts">
import HeaderNav from '@/components/layout/HeaderNav.vue'
import { ref, onMounted } from 'vue'

const isScrolled = ref(false)

// Add scroll listener to adjust header appearance
onMounted(() => {
  function onScroll() {
    isScrolled.value = window.scrollY > 0
  }
  window.addEventListener('scroll', onScroll)
  return () => window.removeEventListener('scroll', onScroll)
})
</script>

<template>
  <header :class="{ 'is-scrolled': isScrolled }">
    <RouterLink :to="{ name: 'home' }" class="brand">
      <h1><PERSON></h1>
    </RouterLink>

    <HeaderNav />
  </header>
</template>

<style scoped>
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  transition: padding .2s ease-out;

  .brand {
    background: none;
    text-decoration: none;
    opacity: var(--opacity-80);

    h1 {
      font-size: var(--step-1);
      font-weight: 700;
      transition: font-size 0.2s ease-out;
    }

    &:hover {
      opacity: 1;
    }
  }
}

.is-scrolled {
  transition: padding .2s ease-in;
  padding-block: .5rem;

  .brand h1 {
    transition: font-size .2s ease-in;
    font-size: var(--step-0);
  }
}
</style>
