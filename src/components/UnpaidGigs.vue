<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Timestamp } from 'firebase/firestore'
import ActBadgeLogo from './ActBadgeLogo.vue'
import { useEvents } from '../composables/useEvents'
import { Calendar, Check, X, Pencil, RotateCcw } from 'lucide-vue-next'
import { Event } from '../models/Event'
import { useTimeAgo } from '@vueuse/core'

const {
  events,
  isLoading: isLoadingEvents,
  subscribeToEvents,
  updateEvent,
  areDetailsLoading
} = useEvents()

// State for payment editing
const editingPayment = ref<string | null>(null)
const editingFee = ref<string | null>(null)
const paymentDate = ref<string>(new Date().toISOString().split('T')[0])
const paymentAmount = ref<number | null>(null)
const showRecentlyPaid = ref(false)

// Replace the existing cutoffDate computed with this new implementation
const INITIAL_CUTOFF_DATE = new Date('2025-02-20')

const cutoffDate = computed<Date | null>(() => {
  if (!events.value.length) return INITIAL_CUTOFF_DATE

  // Get all past events
  const unpaidEvents = events.value.filter(event => !event.isFeePaid())
  if (!unpaidEvents.length) return null

  // Return a Date object containing the date of the oldest unpaid event that is after the current cut off date.
  const oldestUnpaidDate = unpaidEvents
    .map(event => event.when.toDate())
    .sort((a, b) => a.getTime() - b.getTime())[0]

  return oldestUnpaidDate && oldestUnpaidDate > INITIAL_CUTOFF_DATE
    ? oldestUnpaidDate
    : INITIAL_CUTOFF_DATE
})

// Helper function to create Event instance
const createEventInstance = (eventData: any): Event => {
  const event = new Event(eventData as Event & { id: string })
  event.setSaveCallback(async (id, updates) => {
    await updateEvent(id, updates)
  })
  return event
}

const unpaidGigs = computed<Event[]>((): Event[] => {
  const today = new Date()
  return events.value
    .filter(event => {
      const eventDate = event.when.toDate()
      return !event.isFeePaid() &&
        eventDate <= today &&
        eventDate >= INITIAL_CUTOFF_DATE
    })
    .sort((a, b) => b.when.toDate().getTime() - a.when.toDate().getTime()) as Event[]
})

const totalUnpaid = computed<number>(() => {
  return unpaidGigs.value.reduce((total, event) => total + event.fee(), 0)
})

// Computed set of events that need fees set
const needsFeeSet = computed<Set<string>>(() => {
  return new Set(
    unpaidGigs.value
      .filter(event => !event.fee())
      .map(event => event.id)
  )
})

// Add computed property for recently paid gigs
const recentlyPaidGigs = computed<Event[]>((): Event[] => {
  const twoWeeksAgo = new Date()
  twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14)
  twoWeeksAgo.setHours(0, 0, 0, 0)

  return events.value
    .filter(event => {
      const paymentDate = event.feePaymentDate()
      return event.isFeePaid() && paymentDate && (paymentDate >= twoWeeksAgo)
    })
    .sort((a, b) => {
      const dateA = a.feePaymentDate()
      const dateB = b.feePaymentDate()
      return dateB!.getTime() - dateA!.getTime()
    }) as Event[]
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const formatDate = (date: string | Date) => {
  const dateObj = date instanceof Date ? date : new Date(date)
  const formattedDate = dateObj.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'numeric',
    year: '2-digit'
  })
  const day = dateObj.toLocaleDateString('en-GB', {
    weekday: 'short'
  })
  return `${day} ${formattedDate}`
}

const startPaymentEdit = (event: Event) => {
  editingPayment.value = event.id
  paymentAmount.value = event.fee() ? event.fee() : null
  paymentDate.value = new Date().toISOString().split('T')[0]
}

const cancelPaymentEdit = () => {
  editingPayment.value = null
  paymentAmount.value = null
}

const savePayment = async (event: Event) => {
  try {
    // Set the save callback before updating
    event.setSaveCallback(async (id, updates) => {
      await updateEvent(id, updates)
    })

    // Update the event with both fee and payment status
    await event.setPaymentDetails('fee', {
      amount: paymentAmount.value || event.fee(),
      paid: true,
      date: paymentDate.value ? Timestamp.fromDate(new Date(paymentDate.value)) : null
    })

    // Refresh events after update
    await initializeEvents()

    editingPayment.value = null
    paymentAmount.value = null
  } catch (error) {
    console.error('Error saving payment:', error)
  }
}

// Add method to handle fee-only updates
const saveFee = async (event: Event) => {
  try {
    // Set the save callback before updating
    event.setSaveCallback(async (id, updates) => {
      await updateEvent(id, updates)
    })

    // Update the event with the fee
    await event.setPaymentDetails('fee', {
      amount: paymentAmount.value
    })

    // Refresh events after update
    await subscribeToEvents()
    editingFee.value = null
    paymentAmount.value = null
  } catch (error) {
    console.error('Error saving fee:', error)
  }
}

// Modify startPaymentEdit to handle fee-only mode
const startFeeEdit = (event: Event) => {
  editingFee.value = event.id
  paymentAmount.value = event.fee() || 0
}

const cancelFeeEdit = () => {
  editingFee.value = null
  paymentAmount.value = null
}

// Add method to clear fee
const clearFee = async (event: Event) => {
  try {
    // Set the save callback before updating
    event.setSaveCallback(async (id, updates) => {
      await updateEvent(id, updates)
    })

    // Clear the fee
    await event.setPaymentDetails('fee', { amount: 0 })
    editingFee.value = null
  } catch (error) {
    console.error('Error clearing fee:', error)
  }
}

// Add method to undo payment
const undoPayment = async (event: Event) => {
  try {
    // Set the save callback before updating
    event.setSaveCallback(async (id, updates) => {
      await updateEvent(id, updates)
    })

    // Update the event to mark it as unpaid
    await event.setPaymentDetails('fee', {
      paid: false,
      date: null
    })

    // Refresh events after update
    await initializeEvents()
  } catch (error) {
    console.error('Error undoing payment:', error)
  }
}

const initializeEvents = async () => {
  try {
    const twoWeeksAgo = new Date()
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14)
    twoWeeksAgo.setHours(0, 0, 0, 0)

    // Use the earliest date between cutoff and two weeks ago
    const fromDate = cutoffDate.value && cutoffDate.value < twoWeeksAgo
      ? cutoffDate.value
      : twoWeeksAgo

    await subscribeToEvents({
      fromDate,
      includePaid: true
    })
  } catch (err) {
    console.error('Failed to initialize events:', err)
  }
}

// Modify the onMounted hook to only call initializeEvents once
onMounted(async () => {
  await initializeEvents()
})

// Create a computed array of variants
const buttonVariants = computed(() => {
  return Array.from({ length: 5 }, (_, i) => {
    const buttonDate = new Date(Date.now() - (5 - (i + 1)) * 86400000).toISOString().split('T')[0]
    const variant = buttonDate === paymentDate.value ? 'solid' : 'ghost'
    return variant
  })
})
</script>

<template>
  <div class="unpaid-gigs">
    <div v-if="isLoadingEvents || areDetailsLoading" class="loading">
      <p>{{ isLoadingEvents ? 'Loading events...' : 'Loading event details...' }}</p>
    </div>

    <div class="gigs-grid" v-else-if="unpaidGigs.length">
      <div v-for="event in unpaidGigs" :key="event.id" class="gig-card" :class="{
        'editing': editingPayment === event.id,
        'needs-fee': needsFeeSet.has(event.id)
      }">
        <div class="gig-header">
          <div class="date">{{ formatDate(event.when.toDate()) }} <small class="event-date-timeago">{{
            useTimeAgo(event.when.toDate())
              }}</small></div>
          <div class="fee-edit-form">
            <template v-if="editingFee === event.id">
              <input type="number" min="0" v-model="paymentAmount" class="amount-input" step="0.01"
                placeholder="Enter fee amount" />

              <BaseButton @click="saveFee(event)" size="tiny" purpose="success" :disabled="!paymentAmount"
                class="fee-submit-button">
                <Check class="icon" />
              </BaseButton>
              <BaseButton v-show="paymentAmount" @click="clearFee(event)" size="tiny" purpose="warning"
                class="fee-reset-button" title="Clear fee">
                <RotateCcw class="icon" />
              </BaseButton>
              <BaseButton @click="cancelFeeEdit" size="tiny" purpose="danger" class="fee-cancel-button">
                <X class="icon" />
              </BaseButton>
            </template>
            <template v-else>
              <div class="fee" @click="startFeeEdit(event)">
                {{ event.fee() ? formatCurrency(event.fee()) : '' }}
                <template v-if="event.fee()">
                  <BaseButton @click="clearFee(event)" size="tiny" purpose="warning" title="Clear fee">
                    <RotateCcw class="icon" />
                  </BaseButton>
                  <BaseButton @click="startFeeEdit(event)" size="tiny" purpose="primary" title="Edit fee">
                    <Pencil class="icon" />
                  </BaseButton>
                </template>
                <!--
                <BaseButton v-else @click="startFeeEdit(event)" size="tiny" purpose="secondary" variant="outline">
                  Set fee
                </BaseButton>
                -->
              </div>
            </template>
          </div>
          <div v-if="event.deposit()" class="deposit" :class="{ 'deposit--paid': event.isDepositPaid() }">
            {{ formatCurrency(event.deposit()) }} deposit
          </div>
        </div>

        <div class="gig-body">
          <div class="venue">{{ event.venueName() || 'Unknown Venue' }}</div>
          <div class="acts-row">
            <ActBadgeLogo v-for="actId in event.acts.sort()" :key="actId" :actId="actId" size="1.2em" />
          </div>
        </div>

        <div class="gig-actions">
          <template v-if="editingPayment === event.id">
            <div class="payment-form">
              <div v-if="!event.fee()" class="fee-input-section">
                <label>Set fee amount</label>
                <input type="number" v-model="paymentAmount" class="amount-input" step="0.01"
                  placeholder="Enter fee amount" required />
              </div>

              <div class="date-selection">
                <input type="date" v-model="paymentDate" class="date-input" />
                <div class="quick-dates">
                  <BaseButton v-for="i in 5" :key="i" :variant="buttonVariants[i - 1]" size="tiny" @click="() => {
                    const newDate = new Date(Date.now() - (5 - i) * 86400000).toISOString().split('T')[0]
                    paymentDate = newDate  // Remove .value here
                  }">
                    {{ new Date(Date.now() - (5 - i) * 86400000).toLocaleDateString('en-GB', {
                      weekday: 'short',
                      day: 'numeric'
                    }) }}
                  </BaseButton>
                </div>
              </div>

              <div class="form-actions">
                <BaseButton @click="cancelPaymentEdit" purpose="danger">Cancel</BaseButton>
                <BaseButton @click="savePayment(event)" :disabled="!event.fee() && !paymentAmount" purpose="success">
                  <Check class="icon" />Save
                </BaseButton>
              </div>
            </div>
          </template>
          <template v-else>
            <BaseButton @click="startPaymentEdit(event)" variant="solid" style="justify-self: right">
              <Calendar class="icon" />
              {{ event.fee() ? 'Mark as Paid' : 'Set Fee & Pay' }}
            </BaseButton>
          </template>
        </div>
      </div>
    </div>

    <div class="total-container">
      <div v-if="unpaidGigs.length">
        <span class="highlight">{{ formatCurrency(totalUnpaid) }}</span>
        outstanding from {{ unpaidGigs.length }} gig{{ unpaidGigs.length !== 1 ? 's' : '' }}
        <template v-if="needsFeeSet.size">
          <br>
          <li class="warning-text">
            {{ needsFeeSet.size }} gig{{ needsFeeSet.size !== 1 ? 's' : '' }}
            need{{ needsFeeSet.size === 1 ? 's' : '' }} fee to be set

          </li>
        </template>
      </div>
      <div v-else>
        <small>No outstanding payments.</small>
      </div>
    </div>


    <!-- Recently Paid Gigs Section -->
    <div v-if="recentlyPaidGigs.length" class="recently-paid-section">
      <BaseToggle v-model="showRecentlyPaid" size="compact" class="recently-paid-toggle">
        <h3>Show recently paid</h3>
      </BaseToggle>
      <div v-show="showRecentlyPaid">
        <div v-for="event in recentlyPaidGigs" :key="event.id" class="paid-gig-card">
          <span class="paid-gig-info">{{ formatDate(event.date) }} – {{ event.venueName() }}</span>
          <div class="payment-date">
            £{{ event.fee() }} – {{ formatDate(event.feePaymentDate()!) }}
            <BaseButton @click="undoPayment(event)" variant="ghost" purpose="danger" size="tiny"
              class="undo-payment-btn" title="Undo payment">
              <RotateCcw class="icon" />
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.unpaid-gigs {
  display: grid;
  grid-template-rows: auto 1fr;
  gap: var(--space-m);
  line-height: 1;
}

.recently-paid-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-block: var(--space-s) var(--space-xs);
}

.recently-paid-section h3 {
  font-size: var(--step-0);
  margin-bottom: var(--space-3xs);
  color: var(--color-text-light);
}

.paid-gig-card {
  display: grid;
  gap: var(--space-3xs);
  padding: var(--space-3xs);
  font-size: var(--step--1);
}

.paid-gig-info {
  color: var(--color-text-soft)
}

.payment-date {
  display: flex;
  align-items: center;
  gap: var(--space-3xs);
  color: var(--color-text-muted);
}

.paid-gig-card .date,
.paid-gig-card .venue {
  font-size: var(--step--1);
}

.event-date-timeago {
  font-size: var(--step--1);
}

.gigs-grid {
  display: grid;
  gap: var(--space-s);
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.gig-card {
  display: grid;
  gap: var(--space-3xs);
  background: var(--color-bg-3);
  border-radius: var(--radius-sm);
  padding: var(--space-xs);
  margin-bottom: var(--space-3xs);
  transition: background-color 0.2s ease;
  border: 1px solid var(--color-border);
}

.gig-card:last-child {
  margin-bottom: 0;
}

.gig-header {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--space-xs);
}

.gig-card .date {
  font-weight: 700;
  font-size: var(--step-2);
}

.venue {
  color: var(--color-text-light);
  overflow: hidden;
  text-overflow: ellipsis;
}

.gig-card .venue {
  font-size: var(--step-1);
}

.fee {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3xs);
}

.fee-edit-form {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr auto auto;
  grid-template-areas: 'input input submit' '. reset cancel';
  gap: var(--space-3xs);
  align-items: center;

  .amount-input {
    grid-area: input;
    font-size: var(--step--1);
    width: 100%;
  }

  .fee-submit-button {
    grid-area: submit;
    width: fit-content;
  }

  .fee-reset-button {
    grid-area: reset;
    width: fit-content;
  }

  .fee-cancel-button {
    grid-area: cancel;
    width: fit-content;
  }
}


.fee-input-section {
  display: grid;
  gap: var(--space-3xs);
  margin-bottom: var(--space-xs);
}

.fee-input-section label {
  font-size: var(--step--1);
  color: var(--color-text-light);
}

.total-container {
  font-size: var(--step-2);

  >* {
    font-weight: 700;
  }
}

.highlight {
  font-size: var(--step-2);
  font-weight: 700;
}

.warning-text {
  margin-block-start: var(--space-3xs);
  font-size: var(--step-0);
  color: var(--color-warning);
}

.warning {
  color: var(--color-warning);
}

.acts-row {
  :not(:first-child) {
    margin-left: calc(var(--space-4xs) * -1);
  }
}

.deposit {
  font-size: var(--step--1);
  color: var(--color-text-light);
}

.gig-actions {
  display: grid;
  gap: var(--space-3xs);
  justify-content: center;
}

.payment-form {
  display: grid;
  gap: var(--space-3xs);
}

.form-actions {
  display: flex;
  gap: var(--space-3xs);
  justify-content: flex-end;
}

.date-input {
  padding: var(--space-3xs) var(--space-2xs);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
  width: 100%;
}

.loading {
  color: var(--color-text-light);
  font-style: italic;
}

.amount-input::placeholder {
  color: var(--color-text-light);
  font-style: italic;
}

.quick-dates {
  display: flex;
  gap: var(--space-3xs);
  font-size: var(--step--2);
  justify-content: center;
  margin-block-start: var(--space-3xs);
}
</style>
