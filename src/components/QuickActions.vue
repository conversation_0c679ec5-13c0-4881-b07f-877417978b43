<script setup lang="ts">
import { useRouter } from 'vue-router'
import { CalendarPlus, Receipt, CalendarClock, ImagePlus, Clock } from 'lucide-vue-next'
import TasksButton from './tasks/TasksButton.vue'

const router = useRouter()

function createEvent() {
  router.push({ name: 'events.create' })
}

function manageInvoices() {
  alert('Opening Invoices module...')
}

function openRehearsalPlanner() {
  alert('Launching Rehearsal Planner...')
}

function updatePromoAssets() {
  alert('Opening Promo Assets library...')
}

function updateAvailability() {
  alert('Updating Artist Availability...')
}
</script>

<template>
  <BaseSection title="Quick Actions">
    <div class="actions">
      <BaseButton @click="createEvent">
        <CalendarPlus class="icon" />
        New Event
      </BaseButton>
      <TasksButton />
      <BaseButton @click="manageInvoices" disabled>
        <Receipt class="icon" />
        Manage Invoices
      </BaseButton>
      <BaseButton @click="openRehearsalPlanner" disabled>
        <CalendarClock class="icon" />
        Rehearsal Planner
      </BaseButton>
      <BaseButton @click="updatePromoAssets" disabled>
        <ImagePlus class="icon" />
        Update Promo Assets
      </BaseButton>
      <BaseButton @click="updateAvailability" disabled>
        <Clock class="icon" />
        Update Availability
      </BaseButton>
    </div>
  </BaseSection>
</template>

<style scoped>
.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  color: var(--color-text);
}
</style>
