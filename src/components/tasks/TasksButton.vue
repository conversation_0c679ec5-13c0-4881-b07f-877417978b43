<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ListCheck } from 'lucide-vue-next'
import { useTasks } from '@/composables/useTasks'

const router = useRouter()
const { urgentTasks, incompleteTasks, subscribeToTasks, cleanup } = useTasks()

// Add computed properties for task status
const taskStatus = computed(() => {
  const now = new Date()
  const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000))

  const hasOverdue = incompleteTasks.value.some(task =>
    task.dueDate && task.dueDate < now
  )

  const hasNearDue = !hasOverdue && incompleteTasks.value.some(task =>
    task.dueDate && task.dueDate > now && task.dueDate <= threeDaysFromNow
  )

  if (hasOverdue) return 'overdue'
  if (hasNearDue) return 'near-due'
  return incompleteTasks.value.length > 0 ? 'pending' : null
})

function manageTasks() {
  router.push({ name: 'tasks' })
}

onMounted(() => {
  // Subscribe to tasks
  subscribeToTasks()
})

onUnmounted(() => {
  // Cleanup subscriptions
  cleanup()
})
</script>

<template>
  <BaseButton @click="manageTasks" style="position: relative;">
    <ListCheck class="icon" />
    <slot>Tasks</slot>
    <span class="notification-dot not-good" :class="[
      `status-${taskStatus}`,
      { throb: urgentTasks.length > 0 }
    ]">
      {{ incompleteTasks.length }}
    </span>
  </BaseButton>
</template>

<style scoped>
.notification-dot {
  position: absolute;
  display: grid;
  place-items: center;
  top: -5px;
  right: 0;
  min-width: 1.5em;
  min-height: 1.5em;
  border-radius: 1.5em;
  font-size: 13px;
  font-weight: 600;
  padding-inline: 0.4em;
  color: white;
  box-shadow: 0 2px 2px #0008;
}

.status-pending {
  background-color: var(--color-success);
}

.status-near-due {
  background-color: var(--color-warning);
}

.not-good {
  background-color: var(--color-danger-dark);
}

.status-overdue {
  background-color: var(--color-danger-dark);
}

.throb {
  animation: throb 2s infinite;
}

@keyframes throb {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}
</style>
