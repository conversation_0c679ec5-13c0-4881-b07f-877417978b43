<script setup lang="ts">
import { Music, User } from 'lucide-vue-next'
import EntityActions from '@/components/base/EntityActions.vue'
import EntityImage from '@/components/base/EntityImage.vue'
import { useEntityCard } from '@/composables/useEntityCard'
import { Artist } from '@/models/Artist'

type Props = {
  artist: Artist
  grid?: boolean
}

const props = defineProps<Props>()

const {
  isLoading,
  error,
  imageLoaded,
  imageError,
  handleImageLoad,
  handleImageError,
  viewEntity,
  editEntity,
  deleteEntity
} = useEntityCard({
  entityType: 'artist',
  entity: props.artist
})
</script>

<template>
  <BaseCard class="artist-card" :class="{ 'artist-card--grid': grid }">
    <template #header>
      <div class="artist-card__photo">
        <EntityImage v-if="artist.photoUrl" :publicId="artist.photoUrl"
          :alt="`Photo of ${artist.firstName} ${artist.lastName}`" height="200" :fit="true" rounded
          @load="handleImageLoad" @error="handleImageError">
          <div v-if="!imageLoaded && !imageError" class="artist-card__loading">
            Loading...
          </div>
          <div v-if="imageError" class="artist-card__placeholder">
            <User class="artist-card__placeholder-icon" />
          </div>
        </EntityImage>
      </div>
    </template>

    <div class="artist-card__content">
      <div class="artist-card__info">
        <h3 class="artist-card__name">
          {{ artist.stageName || `${artist.firstName} ${artist.lastName}` }}
        </h3>
        <div v-if="artist.instruments" class="artist-card__instruments">
          <Music class="icon" />
          <span>{{ artist.instruments }}</span>
        </div>
      </div>

      <p v-if="error" class="error-message">{{ error }}</p>
    </div>

    <template #footer>
      <EntityActions :is-loading="isLoading" @view="viewEntity" @edit="editEntity" @delete="deleteEntity" />
    </template>
  </BaseCard>
</template>

<style scoped>
.artist-card {
  container-type: inline-size;
}

.artist-card__photo {
  position: relative;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
}

.artist-card__loading,
.artist-card__placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-mute);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.artist-card__placeholder-icon {
  width: 3rem;
  height: 3rem;
  opacity: 0.5;
}

.artist-card__content {
  display: grid;
  gap: 0.5rem;
}

.artist-card__info {
  display: grid;
  gap: 0.25rem;
}

.artist-card__name {
  color: var(--color-heading);
  font-size: var(--step-1);
  margin: 0;
  line-height: 1.2;
}

.artist-card__instruments {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.icon {
  width: 1em;
  height: 1em;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}

/* Grid Layout */
.artist-card--grid {
  height: 100%;
}

@container card (max-width: 300px) {
  .artists-list .artist-card__content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 0.5rem;
  }

  .artists-list .artist-card__info {
    justify-items: center;
  }

  .artists-list .artist-card__instruments {
    justify-content: center;
  }
}
</style>
