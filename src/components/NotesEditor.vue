<script setup lang="ts">
import { ref } from 'vue'
import type { PropType } from 'vue'
import { PlusCircle, Trash2, Edit, Clock, User } from 'lucide-vue-next'
import { useTimeAgo } from '@vueuse/core'

type Note = {
  content: string
  createdAt?: Date | { toDate(): Date }
  updatedAt?: Date | { toDate(): Date }
  createdBy?: {
    email: string
    displayName: string
  }
}

type Notes = {
  [key: string]: Note | string
}

const props = defineProps({
  notes: {
    type: Object as PropType<Notes>,
    default: () => ({})
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  viewOnly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:notes'])

const isAddingNote = ref(false)
const newNote = ref({ title: '', content: '' })
const noteToDelete = ref<string | null>(null)
const showDeleteDialog = ref(false)

function formatTimeAgo(note: Note | string): string {
  // Handle string case
  if (typeof note === 'string') return ''

  // Validate note object has required fields
  if (!note || typeof note !== 'object') return ''

  // Get timestamp, preferring updatedAt over createdAt
  const timestamp = note.updatedAt || note.createdAt
  if (!timestamp) return ''

  try {
    // Handle both Firebase Timestamp and regular Date objects
    const date = 'toDate' in timestamp ? timestamp.toDate() : timestamp

    // Validate we have a valid date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return ''
    }

    // Get relative time string
    const timeAgo = useTimeAgo(date)
    return timeAgo.value || ''
  } catch (error) {
    console.error('Error formatting time ago:', error)
    return ''
  }
}

function addNote() {
  if (!newNote.value.title.trim()) return

  const updatedNotes = {
    ...props.notes,
    [newNote.value.title]: {
      content: newNote.value.content,
      createdAt: new Date(),
      createdBy: {
        email: '<EMAIL>', // This should come from auth context
        displayName: 'User' // This should come from auth context
      }
    }
  }

  emit('update:notes', updatedNotes)
  newNote.value = { title: '', content: '' }
  isAddingNote.value = false
}

function editNote(title: string, note: Note | string) {
  isAddingNote.value = true
  newNote.value = {
    title,
    content: typeof note === 'string' ? note : note.content
  }
}

function deleteNote(title: string) {
  const updatedNotes = { ...props.notes }
  delete updatedNotes[title]
  emit('update:notes', updatedNotes)
  showDeleteDialog.value = false
  noteToDelete.value = null
}

function confirmDelete(title: string) {
  noteToDelete.value = title
  showDeleteDialog.value = true
}
</script>

<template>
  <div class="notes-editor">
    <div class="section-header">
      <h2>Notes</h2>
      <BaseButton v-if="!isAddingNote && !viewOnly" @click="isAddingNote = true" class="add-note-button"
        title="Add new note" size="compact">
        <PlusCircle class="icon" :size="16" />
        <span>Add Note</span>
      </BaseButton>
    </div>

    <!-- New Note Form -->
    <BaseCard v-if="isAddingNote && !viewOnly" class="new-note-form">
      <BaseInput v-model="newNote.title" placeholder="Note Title" class="note-title-input" />
      <BaseEditor v-model="newNote.content" placeholder="Note Content" class="note-content-editor" />
      <div class="form-actions">
        <BaseButton @click="addNote" :disabled="!newNote.title.trim()">
          Save Note
        </BaseButton>
        <BaseButton @click="isAddingNote = false" variant="secondary">
          Cancel
        </BaseButton>
      </div>
    </BaseCard>

    <!-- Existing Notes -->
    <div v-if="Object.keys(notes).length > 0" class="notes-list">
      <BaseCard v-for="[title, note] of (Object.entries(notes) as [string, Note | string][])" :key="title"
        class="note-item">
        <details class="note-details">
          <summary class="note-header">
            <h3 class="note-title">{{ title }}</h3>
            <div v-if="typeof note !== 'string'" class="note-metadata">
              <span v-if="note.createdBy" class="note-author">
                <User :size="16" /> {{ note.createdBy.displayName || note.createdBy.email }}
              </span>
              <span v-if="note.updatedAt || note.createdAt" class="note-timestamp">
                <Clock :size="16" /> {{ formatTimeAgo(note) }}
              </span>
            </div>
            <div v-if="!viewOnly" class="note-actions">
              <BaseButton @click="editNote(title, note)" variant="secondary" class="edit-button" title="Edit note">
                <Edit class="icon" />
              </BaseButton>
              <BaseButton @click="confirmDelete(title)" variant="danger" class="delete-button" title="Delete note">
                <Trash2 class="icon" />
              </BaseButton>
            </div>
          </summary>
          <div class="note-content" v-html="typeof note === 'string' ? note : note.content"></div>
        </details>
      </BaseCard>
    </div>
    <p v-else class="no-notes">No notes added yet</p>

    <!-- Delete Confirmation Dialog -->
    <BaseDialog v-if="!viewOnly" v-model="showDeleteDialog" title="Delete Note" dialog-class="delete-dialog">
      <template #default>
        <p>Are you sure you want to delete this note?</p>
        <p class="note-title-preview">{{ noteToDelete }}</p>
      </template>

      <template #actions>
        <BaseButton @click="deleteNote(noteToDelete!)" variant="danger" :disabled="isDisabled">
          Delete
        </BaseButton>
        <BaseButton @click="showDeleteDialog = false; noteToDelete = null" variant="secondary" :disabled="isDisabled">
          Cancel
        </BaseButton>
      </template>
    </BaseDialog>
  </div>
</template>

<style scoped>
.notes-editor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.add-note-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.new-note-form {
  padding: 1rem;
  margin-bottom: 1rem;
}

.note-title-input {
  margin-bottom: 1rem;
}

.note-content-editor {
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.note-item {
  padding: 0;
}

.note-details {
  padding: 1rem;
  background: var(--color-background-soft);
  border-radius: 0.5rem;
}

.note-header {
  display: flex;
  gap: var(--space-s);
  align-items: flex-end;
  margin-bottom: 0.5rem;
  list-style: none;
  cursor: pointer;
}

.note-header::-webkit-details-marker {
  display: none;
}

.note-title {
  margin: 0;
  font-size: var(--step-0);
  color: var(--color-heading);
  font-weight: 500;
}

.note-metadata {
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  gap: var(--space-s);
  color: var(--color-text-muted);
  font-size: var(--step--2);
}

.note-author {
  font-weight: 500;
}

.note-timestamp {
  font-style: italic;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
  margin-inline-start: auto;
}

.note-content {
  margin-top: 0.75rem;
  color: var(--color-text);
  line-height: 1.5;
}

.note-content :deep(p) {
  margin: 0.5rem 0;
}

.note-content :deep(p:first-child) {
  margin-top: 0;
}

.note-content :deep(p:last-child) {
  margin-bottom: 0;
}

.no-notes {
  color: var(--color-text-light);
  font-style: italic;
}

:deep(.notes-dialog) {
  max-width: 500px;
}

:deep(.delete-dialog) {
  max-width: 400px;
}

.note-title-preview {
  margin-top: 0.5rem;
  font-weight: 500;
  color: var(--color-heading);
}

details[open] .note-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
}

details:not([open]) .note-header {
  margin-bottom: 0;
}
</style>
