<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import ActBadgeLogo from './ActBadgeLogo.vue'
import { useEvents } from '../composables/useEvents'
import { Event } from '../models/Event'
import DateRangeSelector from './DateRangeSelector.vue'

type ActCombination = {
  acts: string[]
  revenue: number
  eventCount: number
}

// Local date state
const fromDate = ref(new Date()) // Default filter date (today)
const selectedFromDate = ref<Date | null>(null) // Explicitly selected start date
const selectedToDate = ref<Date | null>(null) // Explicitly selected end date

const computedFromDate = computed(() => selectedFromDate.value || fromDate.value)

const { events, isLoading: isLoadingEvents, subscribeToEvents } = useEvents()

// State for filtered act combinations
const filteredCombinations = ref(new Set<string>())

// State for year selection
const selectedYear = ref<string>('')

// Watch for year selection changes
watch(selectedYear, (newYear) => {
  if (newYear) {
    const year = parseInt(newYear)

    if (typeof year !== 'number') {
      console.error('Invalid year:', year)
      return
    }

    const start = new Date(year, 0, 1)
    const end = new Date(year, 11, 31)
    end.setHours(23, 59, 59, 999)

    selectedFromDate.value = start
    selectedToDate.value = end
  }
})

// Watch for date changes to clear year selection when dates are cleared
watch([selectedFromDate, selectedToDate], async ([newFromDate, newToDate]) => {
  // If we're clearing the dates
  if (!newFromDate && !newToDate) {
    selectedYear.value = ''
  }

  await initializeEvents()
})

// Helper to get a unique key for an act combination
function getActCombinationKey(acts: string[]): string {
  return [...acts].sort().join('|')
}

// Helper to check if event is within date range
function isEventInDateRange(event: Event): boolean {
  const eventDate = event.when.toDate()

  if (eventDate < computedFromDate.value || (selectedToDate.value && eventDate > selectedToDate.value)) {
    return false
  }

  return true
}

// Computed property for filtered events
const filteredEvents = computed((): Event[] =>
  events.value.map(eventData =>
    new Event(eventData as Event & { id: string })
  ).filter((event) => isEventInDateRange(event))
)

// Modified computed property to use filtered events
const actCombinations = computed((): ActCombination[] => {
  const combinations = new Map<string, ActCombination>()

  filteredEvents.value.forEach(event => {
    const key = getActCombinationKey(event.acts)
    if (!combinations.has(key)) {
      combinations.set(key, {
        acts: event.acts,
        revenue: 0,
        eventCount: 0
      })
    }

    const combo = combinations.get(key)!
    combo.revenue += event.fee() || 0
    combo.eventCount++
  })

  return Array.from(combinations.values())
    .sort((a, b) => b.revenue - a.revenue)
})

const totalRevenue = computed((): number => {
  return actCombinations.value.reduce((total, combo) => {
    if (!filteredCombinations.value.has(getActCombinationKey(combo.acts))) {
      return total + combo.revenue
    }
    return total
  }, 0)
})

// Methods
const toggleCombinationFilter = (acts: string[]): void => {
  const key = getActCombinationKey(acts)
  if (filteredCombinations.value.has(key)) {
    filteredCombinations.value.delete(key)
  } else {
    filteredCombinations.value.add(key)
  }
}

const isCombinationFiltered = (acts: string[]): boolean => {
  return filteredCombinations.value.has(getActCombinationKey(acts))
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Initialize data
async function initializeEvents() {
  try {
    await subscribeToEvents({
      fromDate: computedFromDate.value,
      toDate: selectedToDate.value ?? undefined,
      includePaid: true
    })
  } catch (err) {
    console.error('Failed to initialize events:', err)
  }
}

onMounted(async () => {
  await initializeEvents()
})
</script>

<template>
  <div class="upcoming-revenue" :class="{ 'is-loading': isLoadingEvents }">
    <div class="date-controls">
      <div class="selectors-container">
        <DateRangeSelector v-model:fromDate="selectedFromDate" v-model:toDate="selectedToDate"
          v-model:selectedYear="selectedYear" :defaultFromDate="fromDate" showYearSelector :showSummary="true" />
      </div>
    </div>

    <Transition name="fade" mode="out-in">
      <div v-if="!isLoadingEvents" key="content" class="revenue-section">
        <div class="revenue-table shadow">
          <div class="table-header">
            <div class="header-bottom">
              <div class="cell-acts">Acts</div>
              <div class="cell-count">Gigs</div>
              <div class="cell-revenue">Revenue</div>
            </div>
          </div>

          <div v-for="combo in actCombinations" :key="getActCombinationKey(combo.acts)" class="table-row"
            :class="{ 'filtered': isCombinationFiltered(combo.acts), 'zero-revenue': combo.revenue === 0 }"
            @click="toggleCombinationFilter(combo.acts)">
            <div class="cell-acts">
              <div class="acts-row">
                <ActBadgeLogo v-for="actId in combo.acts" :key="actId" :actId="actId" size="1.5em" class="act-logo" />
              </div>
            </div>
            <div class="cell-count">{{ combo.eventCount }}</div>
            <div class="cell-revenue">{{ formatCurrency(combo.revenue) }}</div>
          </div>
        </div>

        <div class="total-revenue" v-if="totalRevenue > 0">
          <span class="highlight">{{ formatCurrency(totalRevenue) }}</span> total from selected configuration.
        </div>
        <div v-else>
          <small>No upcoming revenue for selected configuration.</small>
        </div>
      </div>
      <div v-else key="loading" class="loading-overlay">
        <span class="loading-text">Loading...</span>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.upcoming-revenue {
  position: relative;
  min-height: 200px;
  display: grid;
  gap: var(--space-m);
  background-color: var(--color-bg-2);
}

.is-loading {
  pointer-events: none;
  opacity: 0.7;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-2);
  border-radius: var(--radius-m);
}

.loading-text {
  color: var(--color-text-light);
}

.revenue-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  width: 100%;
}

.total-revenue {
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-m);
}

.revenue-table {
  display: grid;
  border-radius: var(--radius-m);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.header-bottom {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--space-m);
  padding: var(--space-xs) var(--space-s);
  background-color: var(--color-bg-2);
}

.date-range {
  font-size: var(--step--1);
  color: var(--color-text-light);
  font-weight: normal;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  padding: var(--space-xs) var(--space-s);
  gap: var(--space-m);
  align-items: center;
  cursor: pointer;
  transition: all var(--transition-out);
  background: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border-soft);
}

.table-row:nth-child(odd) {
  background: var(--color-bg-2);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: var(--color-background-soft);
}

.table-row.filtered {
  opacity: 0.5;
  background: var(--color-background-mute);
}

.table-row.zero-revenue {
  color: var(--color-text-light);
  font-style: italic;
}

.cell-acts {
  display: flex;
  align-items: center;
}

.cell-count {
  text-align: right;
  min-width: 3rem;
}

.cell-revenue {
  text-align: right;
  min-width: 5rem;
  font-weight: 500;
}

.acts-row {
  display: flex;
  align-items: center;
}

.highlight {
  color: var(--color-light);
  font-weight: 600;
}

.act-logo {
  &:not(:first-child) {
    margin-left: -.2em;
  }
}

.date-range-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-range-text {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text);
  font-size: var(--step--1);
}

.icon {
  color: var(--color-text-light);
}

.select-dates-btn {
  margin-inline: auto;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  padding: var(--space-xs) 0;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.date-input-group label {
  font-size: var(--step--1);
  font-weight: 500;
  color: var(--color-text);
}

.date-input-group input {
  padding: var(--space-s);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  color: var(--color-text);
  font-size: var(--step-0);
  width: 100%;
  transition: all var(--transition-out);
}

.date-input-group input:hover {
  border-color: var(--color-border-hover);
}

.date-input-group input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 1px var(--color-accent);
}

.quick-select-buttons {
  display: flex;
  gap: var(--space-s);
  justify-content: center;
}

.date-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-s);
  border-radius: var(--radius-m);
  font-size: var(--step--1);
}

.date-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-stat-label {
  color: var(--color-text-light);
  font-weight: 500;
}

.date-stat-value {
  color: var(--color-text);
}

.date-range-actions {
  display: flex;
  gap: var(--space-xs);
  align-items: center;
}

.date-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.selectors-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
  padding: var(--space-s);
  background-color: var(--color-bg-3);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
}

.selectors-row {
  display: flex;
  gap: var(--space-s);
  align-items: center;
}

.selectors-row :deep(.select-wrapper) {
  min-width: 150px;
}

.date-range-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: var(--space-xs);
  border-top: 1px solid var(--color-border-soft);
}

.date-range-row :deep(.date-range-selector) {
  width: auto;
}

.date-range-row :deep(.date-range-summary) {
  margin: 0;
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
