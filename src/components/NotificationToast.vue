<script setup lang="ts">
import { useMessageStore } from '../stores/messageStore'
import { storeToRefs } from 'pinia'
import type { Ref } from 'vue'

type Notification = {
  type: 'success' | 'error' | 'info' | 'warning'
  message: string
}

const messageStore = useMessageStore()
const { currentNotification } = storeToRefs(messageStore) as unknown as {
  currentNotification: Ref<Notification | null>
}
</script>

<template>
  <Teleport to="body">
    <Transition name="notification">
      <div v-if="currentNotification" class="notification" :class="`notification--${currentNotification.type}`">
        {{ currentNotification.message }}
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  background: var(--color-background-soft);
  color: var(--color-text);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 9999;
}

.notification--success {
  background: #059669;
  color: white;
}

.notification--error {
  background: #dc2626;
  color: white;
}

.notification--info {
  background: #2563eb;
  color: white;
}

.notification--warning {
  background: #d97706;
  color: white;
}

/* Transition animations */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}
</style>
