import type { Meta, StoryObj } from '@storybook/vue3-vite'
import BaseSearchSelect from '@/components/base/BaseSearchSelect.vue'

const meta: Meta<typeof BaseSearchSelect> = {
  title: 'Base/SearchSelect',
  component: BaseSearchSelect,
  argTypes: {
    modelValue: { control: 'text' },
    placeholder: { control: 'text' },
    disabled: { control: 'boolean' },
    label: { control: 'text' },
    required: { control: 'boolean' },
    clearable: { control: 'boolean' },
    clearOnClickAway: { control: 'boolean' },
    options: { control: { type: 'object' } },
  },
}
export default meta

type Story = StoryObj<typeof BaseSearchSelect>

const demoOptions = [
  { value: 'option-1', label: 'Option One' },
  { value: 'option-2', label: 'Option Two' },
  { value: 'option-3', label: 'Option Three' },
]

export const Default: Story = {
  args: {
    label: 'Choose an option',
    placeholder: 'Search...',
    options: demoOptions,
    clearable: true,
    clearOnClickAway: false,
    required: false,
    disabled: false,
  },
}

export const Disabled: Story = {
  args: {
    label: 'Disabled Select',
    options: demoOptions,
    disabled: true,
  },
}

export const Preselected: Story = {
  args: {
    label: 'Preselected option',
    options: demoOptions,
    modelValue: 'option-2',
  },
}

export const RequiredAndClearOnClickAway: Story = {
  args: {
    label: 'Search required option',
    options: demoOptions,
    required: true,
    clearOnClickAway: true,
  },
}
