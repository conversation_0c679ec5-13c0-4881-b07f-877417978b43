<script setup lang="ts">
import { ref, watch } from 'vue'
import { UploadIcon, XIcon } from 'lucide-vue-next'

type Props = {
  modelValue?: File | null
  previewUrl?: string | null
  aspectRatio?: number
  maxSize?: number // in bytes
  acceptedTypes?: string[]
}

type Emits = {
  (e: 'update:modelValue', file: File | null): void
  (e: 'error', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  previewUrl: null,
  aspectRatio: 1,
  maxSize: 5 * 1024 * 1024, // 5MB
  acceptedTypes: () => ['image/jpeg', 'image/png', 'image/webp']
})

const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement | null>(null)
const preview = ref<string | null>(props.previewUrl)
const isDragging = ref(false)
const error = ref<string | null>(null)

watch(() => props.previewUrl, (newUrl) => {
  preview.value = newUrl
})

function handleFileSelect(event: Event) {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]

  if (!file) return
  validateAndProcessFile(file)
}

function handleDrop(event: DragEvent) {
  event.preventDefault()
  isDragging.value = false

  const file = event.dataTransfer?.files[0]
  if (!file) return
  validateAndProcessFile(file)
}

function validateAndProcessFile(file: File) {
  error.value = null

  // Validate file type
  if (!props.acceptedTypes.includes(file.type)) {
    error.value = 'Invalid file type. Please upload a JPEG, PNG, or WebP image.'
    emit('error', error.value)
    return
  }

  // Validate file size
  if (file.size > props.maxSize) {
    error.value = `File size must be less than ${props.maxSize / 1024 / 1024}MB`
    emit('error', error.value)
    return
  }

  // Create preview
  const reader = new FileReader()
  reader.onload = (e) => {
    preview.value = e.target?.result as string
  }
  reader.readAsDataURL(file)

  emit('update:modelValue', file)
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  isDragging.value = true
}

function handleDragLeave() {
  isDragging.value = false
}

function clearFile() {
  preview.value = null
  emit('update:modelValue', null)
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

function triggerFileInput() {
  fileInput.value?.click()
}
</script>

<template>
  <div class="photo-upload" :class="{ 'is-dragging': isDragging }" @dragover="handleDragOver"
    @dragleave="handleDragLeave" @drop="handleDrop">
    <input ref="fileInput" type="file" :accept="acceptedTypes.join(',')" class="hidden" @change="handleFileSelect">

    <div v-if="preview" class="preview-container">
      <img :src="preview" alt="Preview" class="preview-image" :style="{ aspectRatio: aspectRatio }">
      <BaseButton icon purpose="secondary" class="clear-button" @click="clearFile" aria-label="Clear photo">
        <XIcon :size="20" />
      </BaseButton>
    </div>

    <div v-else class="upload-placeholder" @click="triggerFileInput">
      <UploadIcon :size="24" />
      <span class="upload-text">
        Drop photo here or click to upload
      </span>
      <span class="upload-hint">
        JPEG, PNG or WebP, max {{ maxSize / 1024 / 1024 }}MB
      </span>
    </div>

    <p v-if="error" class="error-message" role="alert">
      {{ error }}
    </p>
  </div>
</template>

<style scoped>
.photo-upload {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius);
  padding: var(--space-m);
  transition: all 0.2s ease;
  cursor: pointer;
}

.photo-upload:hover,
.photo-upload.is-dragging {
  border-color: var(--color-brand);
  background: var(--color-surface-hover);
}

.preview-container {
  position: relative;
  width: 100%;
  border-radius: var(--radius);
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.clear-button {
  position: absolute;
  top: var(--space-2xs);
  right: var(--space-2xs);
  background: var(--color-surface);
  border-radius: var(--radius-full);
  color: var(--color-text-soft);
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: var(--color-surface-hover);
  color: var(--color-danger);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text-soft);
}

.upload-text {
  font-size: var(--step-0);
  font-weight: 500;
}

.upload-hint {
  font-size: var(--step--1);
  color: var(--color-text-muted);
}

.error-message {
  margin-top: var(--space-xs);
  color: var(--color-danger);
  font-size: var(--step--1);
}

.hidden {
  display: none;
}
</style>
