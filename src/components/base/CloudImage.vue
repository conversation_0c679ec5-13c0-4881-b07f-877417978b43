<script setup lang="ts">
import { AdvancedImage } from '@cloudinary/vue'
import { Cloudinary } from '@cloudinary/url-gen'
import { fill } from '@cloudinary/url-gen/actions/resize';

const cld = new Cloudinary({
  cloud: {
    cloudName: 'dave-collison'
  }
})

const props = defineProps<{
  publicId: string
  crop?: boolean
}>()

const image = cld.image(props.publicId)

if (props.crop) {
  image.resize(fill(250, 180))
}
</script>

<template>
  <AdvancedImage :cldImg="image" />
</template>
