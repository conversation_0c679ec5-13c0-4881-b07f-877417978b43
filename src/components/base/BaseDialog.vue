<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { X } from 'lucide-vue-next'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  dialogClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogRef = ref<HTMLDialogElement | null>(null)

function closeDialog() {
  dialogRef.value?.close()
  emit('update:modelValue', false)
}

function handleEscape(e: KeyboardEvent) {
  if (e.key === 'Escape' && props.modelValue) {
    closeDialog()
  }
}

// Handle clicks on the backdrop (dialog::backdrop)
function handleBackdropClick(e: MouseEvent) {
  const rect = dialogRef.value?.getBoundingClientRect()
  if (rect) {
    const isInDialog = (
      e.clientX >= rect.left &&
      e.clientX <= rect.right &&
      e.clientY >= rect.top &&
      e.clientY <= rect.bottom
    )
    if (!isInDialog) {
      closeDialog()
    }
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})

watch(() => props.modelValue, (value) => {
  if (!dialogRef.value) return

  if (value && !dialogRef.value.open) {
    dialogRef.value.showModal()
  } else if (!value && dialogRef.value.open) {
    dialogRef.value.close()
  }
})
</script>

<template>
  <Teleport to="body">
    <Transition name="dialog">
      <div v-if="modelValue" class="dialog-overlay" @click="handleBackdropClick">
        <div class="base-dialog shadow" @click.stop>
          <header class="dialog-header">
            <h2 class="dialog-title">{{ title }}</h2>
            <button class="close-button" @click="$emit('update:modelValue', false)">
              <X />
            </button>
          </header>
          <div class="dialog-content">
            <slot></slot>
          </div>
          <footer v-if="$slots.actions" class="dialog-footer">
            <slot name="actions"></slot>
          </footer>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  overflow-y: auto;
  padding: var(--space-m);
}

.base-dialog {
  background: var(--color-bg-2);
  border-radius: var(--radius-l);
  width: 100%;
  min-width: 90%;
  max-width: 768px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: var(--space-m);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.dialog-title {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-heading);
}

.dialog-content {
  padding: var(--space-m);
  overflow-y: auto;
}

.close-button {
  background: none;
  border: none;
  padding: var(--space-xs);
  cursor: pointer;
  color: var(--color-text);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-m);
}

.close-button:hover {
  background: var(--color-background-mute);
}

/* Dialog transition animations */
.dialog-enter-active,
.dialog-leave-active {
  transition: opacity 0.2s ease;
}

.dialog-enter-from,
.dialog-leave-to {
  opacity: 0;
}

.dialog-footer {
  padding: var(--space-m);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-xs);
}
</style>
