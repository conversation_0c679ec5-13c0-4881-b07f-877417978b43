<script setup lang="ts">
// This component accepts an array of options. Each option is an object containing a label and a value.
// Each options should be rendered as a radio input
// The currently selected option is passed as a prop
// The component emits a change event when the selected option changes
import { ref } from 'vue'

// define props with types
type Option = {
  label: string
  value: string
}

const props = defineProps({
  options: {
    type: Array<Option>,
    required: true
  },
  value: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['change'])

const selectedValue = ref(props.value)

function handleChange(event: Event) {
  selectedValue.value = (event.target as HTMLInputElement).value
  emit('change', selectedValue.value)
}
</script>

<template>
  <div class="option-select">
    <div v-for="option in options" :key="option.value" class="option-select__option">
      <input type="radio" :id="option.value" :value="option.value" :checked="option.value === value"
        @change="handleChange" />
      <label :for="option.value">{{ option.label }}</label>
    </div>
  </div>

</template>

<style scoped>
.option-select {
  display: flex;

  .option-select__option {
    input {
      display: none;

      &:checked+label {
        background-color: var(--color-brand);
        color: var(--color-background);
        box-shadow: 0 10px 5px -5px #0006 inset;
      }
    }

    label {
      padding: 0.3em .6em;
      cursor: pointer;
      background-color: var(--color-background);
      border: 1px solid var(--color-border);
      transition: background-color var(--transition-out), color var(--transition-out);

      &:hover,
      &:focus-visible {
        background-color: var(--color-brand-darkest);
        transition: background-color var(--transition-in), color var(--transition-in);
      }
    }

    &:first-child label {
      border-radius: var(--radius-m) 0 0 var(--radius-m);
    }

    &:last-child label {
      border-radius: 0 var(--radius-m) var(--radius-m) 0;
    }
  }
}
</style>
