<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ChevronDown as DownArrow, ChevronRight as RightArrow } from 'lucide-vue-next'

type Props = {
  isOpen?: boolean
  placement?: 'top' | 'right' | 'bottom' | 'left'
  offset?: number,
  icon?: boolean,
  size?: 'tiny' | 'compact' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  placement: 'bottom',
  offset: 4,
  icon: false,
  size: 'default'
})

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
}>()

const dropdownRef = ref<HTMLElement | null>(null)
const triggerRef = ref<HTMLElement | null>(null)
const menuRef = ref<HTMLElement | null>(null)
const menuPosition = ref({
  top: 'auto',
  right: 'auto',
  bottom: 'auto',
  left: 'auto',
  transform: 'none'
})

// Compute classes
const dropdownClasses = computed(() => [
  `base-dropdown--${props.size}`,
  {
    'base-dropdown--open': props.isOpen
  }
])

function handleTriggerClick(event: MouseEvent) {
  event.stopPropagation()
  emit('update:isOpen', !props.isOpen)
}

function handleClickOutside(event: MouseEvent) {
  // Don't close if clicking inside dropdown or on trigger
  if (dropdownRef.value?.contains(event.target as Node)) return
  if (triggerRef.value?.contains(event.target as Node)) return

  emit('update:isOpen', false)
}

function updateMenuPosition() {
  if (!menuRef.value || !triggerRef.value) return

  const menu = menuRef.value
  const trigger = triggerRef.value
  const triggerRect = trigger.getBoundingClientRect()
  const menuRect = menu.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // Reset position
  menuPosition.value = {
    top: 'auto',
    right: 'auto',
    bottom: 'auto',
    left: 'auto',
    transform: 'none'
  }

  // Calculate initial position based on placement
  let top = 0
  let left = 0
  let transform = ''

  switch (props.placement) {
    case 'top':
      top = triggerRect.top - menuRect.height - props.offset
      left = triggerRect.left + (triggerRect.width - menuRect.width) / 2
      // transform = 'translateX(-50%)'
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - menuRect.height) / 2
      left = triggerRect.right + props.offset
      // transform = 'translateY(-50%)'
      break
    case 'bottom':
      top = triggerRect.bottom + props.offset
      left = triggerRect.left + (triggerRect.width - menuRect.width) / 2
      // transform = 'translateX(-50%)'
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - menuRect.height) / 2
      left = triggerRect.left - menuRect.width - props.offset
      // transform = 'translateY(-50%)'
      break
  }

  // Check if menu would go off-screen and adjust position
  if (left < 5) {
    left = 5
    transform = transform.replace('translateX(-50%)', '')
  }
  if (left + menuRect.width > viewportWidth - 5) {
    left = viewportWidth - menuRect.width - 5
    transform = transform.replace('translateX(-50%)', '')
  }
  if (top < 5) {
    top = 5
    transform = transform.replace('translateY(-50%)', '')
  }
  if (top + menuRect.height > viewportHeight - 5) {
    top = viewportHeight - menuRect.height - 5
    transform = transform.replace('translateY(-50%)', '')
  }

  menuPosition.value = {
    top: `${top}px`,
    right: 'auto',
    bottom: 'auto',
    left: `${left}px`,
    transform
  }
}

const chevron = computed(() => props.icon ? props.isOpen ? DownArrow : RightArrow : null)

// Watch for isOpen changes to update position
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    // Wait for next tick to ensure menu is rendered
    setTimeout(updateMenuPosition, 0)
  }
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updateMenuPosition)
  window.addEventListener('scroll', updateMenuPosition)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateMenuPosition)
  window.removeEventListener('scroll', updateMenuPosition)
})
</script>

<template>
  <div class="base-dropdown" ref="dropdownRef" :class="dropdownClasses">
    <div ref="triggerRef" @click="handleTriggerClick" class="dropdown-trigger">
      <slot name="trigger" />
      <span v-if="chevron" class="chevron">
        <component :is="chevron" class="icon" />
      </span>
    </div>

    <div v-show="isOpen" ref="menuRef" class="dropdown-menu" :style="menuPosition" role="menu">
      <slot />
    </div>
  </div>
</template>

<style scoped>
:where(.base-dropdown) {
  position: relative;
  display: inline-block;
}

.base-dropdown--tiny {
  font-size: var(--step--2);
}

.base-dropdown--compact {
  font-size: var(--step--1);
}

.base-dropdown--default {
  font-size: var(--step-0);
}

.base-dropdown--large {
  font-size: var(--step-1);
}

.dropdown-trigger {
  display: inline-flex;
  align-items: center;
  gap: var(--space-gap-2xs);
  cursor: pointer;
}

.chevron {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  color: var(--color-text-muted);
  transition: transform var(--transition-fast);
}

/* Optional: Rotate the chevron when dropdown is open */
.chevron :deep(svg) {
  transition: transform var(--transition-fast);
}

.base-dropdown:has([aria-expanded="true"]) .chevron :deep(svg) {
  transform: rotate(180deg);
}

:where(.dropdown-menu) {
  position: fixed;
  display: grid;
  min-width: 8em;
  background: var(--color-bg-1);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 0;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  z-index: 50;
  overflow: hidden;
}

/* Common dropdown item styles that can be used by parent components */
:global(.dropdown-item) {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  font-size: var(--step--1);
  color: var(--color-text);
  background: none;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  text-decoration: none;
  transition: all var(--transition-fast);
}

:global(.dropdown-item:hover) {
  background: var(--color-background-soft);
}

:global(.dropdown-divider) {
  height: 1px;
  background: var(--color-border);
  margin: 0.5rem 0;
}
</style>
