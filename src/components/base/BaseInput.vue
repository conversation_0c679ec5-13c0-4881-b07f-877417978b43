<script setup lang="ts">
import { computed } from 'vue'

type Props = {
  modelValue?: string | number | null
  label?: string
  type?: string
  required?: boolean
  placeholder?: string
  disabled?: boolean
  error?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  label: '',
  type: 'text',
  required: false,
  placeholder: '',
  disabled: false,
  error: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number | null]
}>()

const id = computed(() => `input-${Math.random().toString(36).substring(2, 9)}`)

function handleInput(event: Event) {
  const input = event.target as HTMLInputElement
  const value = input.value

  if (props.type === 'number') {
    emit('update:modelValue', value ? Number(value) : null)
  } else {
    emit('update:modelValue', value || null)
  }
}
</script>

<template>
  <div class="base-input">
    <label v-if="label" :for="id" class="input-label">
      {{ label }}
      <span v-if="required" class="required-indicator">*</span>
    </label>

    <input :id="id" :type="type" :value="modelValue" :placeholder="placeholder" :disabled="disabled"
      :required="required" :aria-required="required" :aria-invalid="!!error"
      :aria-describedby="error ? `${id}-error` : undefined" class="input-field" :class="{ 'has-error': error }"
      @input="handleInput">

    <p v-if="error" :id="`${id}-error`" class="error-message" role="alert">
      {{ error }}
    </p>
  </div>
</template>

<style scoped>
.base-input {
  display: grid;
  gap: 0.5rem;
}

.input-label {
  font-size: var(--step--1);
  font-weight: 500;
  color: var(--color-text);
}

.required-indicator {
  color: var(--color-error);
  margin-left: 0.25rem;
}

.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step-0);
  transition: all 0.2s ease;

  &::placeholder {
    color: var(--color-text-muted);
  }

  &:hover {
    border-color: var(--color-border-hover);
  }

  &:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px var(--color-accent-soft);
  }

  &:disabled {
    background: var(--color-background-mute);
    cursor: not-allowed;
    opacity: 0.7;
  }

  &.has-error {
    border-color: var(--color-error);

    &:focus {
      box-shadow: 0 0 0 2px var(--color-error-soft);
    }
  }
}

.error-message {
  font-size: var(--step--1);
  color: var(--color-error);
  margin: 0;
}
</style>
