<script setup lang="ts">
import type { PropType } from 'vue'

type Option = {
  value: string
  label: string
  searchData?: string[]
}

defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  options: {
    type: Array as PropType<Option[]>,
    required: true
  },
  modelValue: {
    type: String,
    required: true
  },
  highlightedIndex: {
    type: Number,
    required: true
  }
})

const emit = defineEmits<{
  'update:highlightedIndex': [value: number]
  'update:isOpen': [value: boolean]
  'update:modelValue': [value: string]
  'change': [value: Option]
}>()

function handleSelect(option: Option) {
  emit('update:modelValue', option.value)
  emit('change', option)
  emit('update:isOpen', false)
  emit('update:highlightedIndex', 0)
}

function updateHighlightedIndex(index: number) {
  emit('update:highlightedIndex', index)
}
</script>

<template>
  <div v-show="isOpen" class="search-select__dropdown" role="listbox">
    <div class="search-select__options">
      <button v-for="(option, index) in options" :key="option.value" type="button" :id="`option-${option.value}`"
        class="search-select__option" role="option" :aria-selected="option.value === modelValue" :class="{
          'search-select__option--selected': option.value === modelValue,
          'search-select__option--highlighted': index === highlightedIndex
        }" @click="handleSelect(option)" @mouseover="updateHighlightedIndex(index)">
        {{ option.label }}
      </button>
      <div v-if="options.length === 0" class="search-select__no-results" role="alert">
        No results found
      </div>
    </div>
  </div>
</template>

<style scoped>
.search-select__dropdown {
  position: absolute;
  top: calc(100% + 0.25rem);
  left: 0;
  right: 0;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
  width: fit-content;
}

.search-select__option {
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text);
  transition: background-color 0.2s ease;
}

.search-select__option:hover {
  background-color: var(--color-brand-alpha);
  outline: 1px solid var(--color-text);
  outline-offset: -2px;
}

.search-select__option--selected {
  background-color: var(--color-brand);
  color: var(--color-white);
}

.search-select__option--highlighted {
  background-color: var(--color-brand-dark-alpha);
}

.search-select__option--selected.search-select__option--highlighted {
  background-color: var(--color-brand-darkest);
}

.search-select__no-results {
  padding: 0.5rem 1rem;
  color: var(--color-text-muted);
  text-align: center;
}
</style>
