<script setup lang="ts">
import { computed } from 'vue'

type EntityImageProps = {
  publicId: string
  alt: string
  height?: number | string
  width?: number | string
  fit?: boolean
  aspectRatio?: string
  rounded?: boolean
}

const props = withDefaults(defineProps<EntityImageProps>(), {
  height: 'auto',
  width: '100%',
  fit: false,
  aspectRatio: '16/9',
  rounded: false
})

const emit = defineEmits(['load', 'error'])

const containerStyle = computed(() => ({
  aspectRatio: props.aspectRatio,
  borderRadius: props.rounded ? 'var(--radius-m)' : undefined
}))

const imageStyle = computed(() => ({
  objectFit: props.fit ? 'cover' : 'contain'
}))
</script>

<template>
  <div class="entity-image" :style="containerStyle">
    <div class="entity-image__placeholder" v-if="!publicId">
      <slot name="placeholder">
        <div class="entity-image__no-image">No image available</div>
      </slot>
    </div>
    <CloudImage v-else :publicId="publicId" :alt="alt" :height="height" :width="width" :style="imageStyle"
      class="entity-image__img" @load="emit('load')" @error="emit('error')" />
    <slot />
  </div>
</template>

<style scoped>
.entity-image {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.entity-image__img {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.entity-image:hover .entity-image__img {
  transform: scale(1.05);
}

.entity-image__placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.entity-image__no-image {
  padding: 1rem;
  text-align: center;
}
</style>
