<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import type { Editor as TinyMCEEditor } from 'tinymce'

type EditorProps = {
  modelValue?: string | null
  placeholder?: string
  height?: number | string
}

const props = withDefaults(defineProps<EditorProps>(), {
  modelValue: '',
  placeholder: '',
  height: 300
})

const emit = defineEmits<{
  'update:modelValue': [content: string | null]
}>()

// TODO: replace hardcoded API key with env variable.
const apiKey = '8h2mb49y1j37xrzjg1vcxyzz5nepvadltucy5gd7cr6ab5jy'

// Extract content from modelValue if it's an object
const getContent = (value: any): string => {
  if (!value) return ''
  if (typeof value === 'string') return value
  if (typeof value === 'object' && value.content) return value.content
  return ''
}

// set variables for colors if user is in dark mode.

const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
const darkModeColor = '#181818'
const lightModeColor = '#f8f8f8'
const darkModeTextColor = '#fffa'
const lightModeTextColor = '#0008'

const init = {
  height: props.height,
  menubar: false,
  plugins: ['lists', 'link'],
  toolbar: 'styles | bold italic underline | bullist numlist | link | undo redo',
  selector: 'textarea',
  skin: (isDarkMode ? 'oxide-dark' : 'oxide'),
  content_css: (isDarkMode ? 'dark' : 'default'),
  content_style: `
  .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
    color: ${isDarkMode ? darkModeTextColor : lightModeTextColor};
  }
  body {
    background-color: ${isDarkMode ? darkModeColor : lightModeColor};
  }
  `,
  style_formats: [
    { title: 'Paragraph', format: 'p' },
    { title: 'Heading 3', format: 'h3' },
    { title: 'Heading 4', format: 'h4' }
  ],
  placeholder: props.placeholder || 'Enter text here...',
  browser_spellcheck: true,
  branding: false,
  statusbar: false,
  min_height: 200,
  max_height: 800,
  resize: true,
  toolbar_mode: 'wrap',
  toolbar_sticky: true,
  toolbar_location: 'top',
  setup: function (editor: TinyMCEEditor) {
    // Store editor reference as soon as it's available
    editorRef.value = editor

    // Set initial content in setup
    editor.on('init', () => {
      const content = getContent(props.modelValue)
      if (content !== editor.getContent()) {
        editor.setContent(content)
      }
    })

    // Handle content changes
    let lastContent = ''
    editor.on('input change keyup', () => {
      const content = editor.getContent()
      if (content !== lastContent) {
        lastContent = content
        emit('update:modelValue', content)
      }
    })
  }
}

const editorRef = ref<TinyMCEEditor | null>(null)

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editorRef.value && editorRef.value.initialized) {
    const content = getContent(newValue)
    if (content !== editorRef.value.getContent()) {
      editorRef.value.setContent(content)
    }
  }
}, { deep: true })

onMounted(() => {
  if (editorRef.value) {
    editorRef.value.setContent(getContent(props.modelValue))
  }
})

onUnmounted(() => {
  if (editorRef.value) {
    editorRef.value.destroy()
  }
})
</script>

<template>
  <div class="base-editor">
    <Editor class="editor" :api-key="apiKey" :init="init" :initial-value="modelValue" />
  </div>
</template>

<style>
.editor[data-mce-placeholder]::before {
  color: orange !important;
}
</style>
