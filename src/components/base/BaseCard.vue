<script setup lang="ts">
type Props = {
  variant?: 'default' | 'outline'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default'
})
</script>

<template>
  <article class="layer card shadow" :class="[
    `card--${props.variant}`,
    { 'card--outline': props.variant === 'outline' }
  ]">
    <div>
      <header v-if="$slots.header" class="layer card-inner card__header">
        <h3>
          <slot name="header" />
        </h3>
      </header>

      <div class="layer card-inner card__content">
        <slot />
      </div>

      <footer v-if="$slots.footer" class="layer card-inner card__footer">
        <slot name="footer" />
      </footer>
    </div>
  </article>
</template>

<style scoped>
.card {
  display: grid;
  height: 100%;
  padding: var(--space-m);
  border-radius: var(--radius-l);
  background-color: var(--color-bg-2);

  &.card--outline {
    border: 1px solid var(--color-border);
  }
}

.card-inner {
  display: grid;
  grid-template-rows: auto 1fr auto;
  transition: transform 0.2s ease;
}

/* Variants */
.card--default {
  border: 1px solid var(--color-border);
}

.card--outline {
  border: 1px solid var(--color-border);
}

/* Header styles */
.card__header {
  margin: 0;

  &:has(+ *) {
    margin-block-end: .5rem;
  }

  h3 {
    color: var(--color-text-soft);
  }
}

/* Content styles */
.card__content {
  display: grid;
  gap: .5rem;

  &:has(+ *) {
    margin-block-end: .5rem;
  }
}

/* Footer styles */
.card__footer {
  margin-top: auto;
}

/* Container query based responsive adjustments */
@container card (max-width: 400px) {
  .card__header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card__footer {
    flex-direction: column;
  }
}
</style>
