<script setup lang="ts">
import ActBadgeLogo from '@/components/ActBadgeLogo.vue'
import { Event } from '@/models/Event'

type Props = {
  event: Event
}

withDefaults(defineProps<Props>(), {}) as {
  event: Event & { [key: string]: any }
}
</script>

<template>
  <RouterLink class="event-strip-link" :to="{ name: 'events.show', params: { id: event.id } }">
    <div class="event-strip" :title="event.shortTitle()">
      <div class="event-strip__content">
        <div class="event-strip__time">{{ event.startTime() }}</div>
      </div>
      <div class="event-strip__acts">
        <ActBadgeLogo v-for="act in event.acts" :key="act" :actId="act" />
      </div>
    </div>
  </RouterLink>
</template>

<style scoped>
.event-strip {
  background-color: var(--color-background-soft);
  border-radius: 4px;
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 1em;
  font-size: var(--step--3);
  padding: 0.4rem 0.6rem;

  &:hover {
    background-color: var(--color-background-soft-hover);
    outline: 1px solid var(--color-brand);
  }
}

.event-strip-link {
  text-decoration: none;
  color: inherit;
}

.event-strip__content {
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.event-strip__time {
  font-weight: 500;
}

.event-strip__venue {
  font-size: var(--step--4);
  color: var(--color-text-muted);
  display: flex;
  gap: 0.4rem;
}

.venue-county {
  &::before {
    content: '•';
    margin-right: 0.4rem;
  }
}

.event-strip__acts {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  justify-content: flex-end;
}
</style>
