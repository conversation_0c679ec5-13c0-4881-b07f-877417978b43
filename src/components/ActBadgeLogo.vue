<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useAct } from '@/composables/useAct'

const props = defineProps({
  actId: {
    type: String,
    required: true
  },
  size: {
    type: String,
    default: '1em'
  },
  alt: {
    type: String,
    default: ''
  }
})

const { act, isLoadingAct, error } = useAct(props.actId)

const publicId = computed(() => {
  if (!act.value) return ''
  return act.value.logoUrls?.badge || ''
})

const altText = computed(() => props.alt || act.value?.name || 'Act Logo')

// Prefetch the next image
const prefetchImage = (url: string): void => {
  if (!url) return
  const img = new Image()
  img.src = url
}

// When the component mounts, prefetch the image
onMounted(() => {
  if (publicId.value) {
    prefetchImage(publicId.value)
  }
})
</script>

<template>
  <CloudImage v-if="publicId && !isLoadingAct && !error" :key="publicId" :publicId="publicId" :alt="altText"
    class="badge-logo" />
</template>

<style scoped>
.badge-logo {
  width: v-bind(size);
  height: v-bind(size);
}
</style>
