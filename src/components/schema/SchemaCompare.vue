<script setup lang="ts">
import { computed } from 'vue'
import { Check, Plus, Minus, ArrowRight } from 'lucide-vue-next'
import type { Schema, SchemaField } from '@/types/schema'

type FieldDiff = {
  name: string
  schema: SchemaField
}

type ModifiedFieldDiff = {
  name: string
  generated: Schema<PERSON>ield
  existing: SchemaField
}

type RequiredChanges = {
  added: string[]
  removed: string[]
}

type Differences = {
  added: FieldDiff[]
  removed: FieldDiff[]
  modified: ModifiedFieldDiff[]
  unchanged: FieldDiff[]
  requiredChanges: RequiredChanges
}

const props = defineProps<{
  generated: Schema
  existing: Schema
}>()

const emit = defineEmits<{
  (e: 'update', schema: Schema): void
}>()

const differences = computed<Differences>(() => {
  const diffs = {
    added: [] as FieldDiff[],
    removed: [] as FieldDiff[],
    modified: [] as ModifiedFieldDiff[],
    unchanged: [] as FieldDiff[],
    requiredChanges: {
      added: [] as string[],
      removed: [] as string[]
    }
  }

  // Compare properties
  const allFields = new Set([
    ...Object.keys(props.generated.properties || {}),
    ...Object.keys(props.existing.properties || {})
  ])

  allFields.forEach(field => {
    const genField = props.generated.properties?.[field]
    const existField = props.existing.properties?.[field]

    if (!existField && genField) {
      diffs.added.push({ name: field, schema: genField })
    } else if (!genField && existField) {
      diffs.removed.push({ name: field, schema: existField })
    } else if (genField && existField && JSON.stringify(genField) !== JSON.stringify(existField)) {
      diffs.modified.push({
        name: field,
        generated: genField,
        existing: existField
      })
    } else if (genField && existField) {
      diffs.unchanged.push({ name: field, schema: genField })
    }
  })

  // Compare required fields
  const genRequired = new Set(props.generated.required || [])
  const existRequired = new Set(props.existing.required || [])

  diffs.requiredChanges = {
    added: [...genRequired].filter(f => !existRequired.has(f)),
    removed: [...existRequired].filter(f => !genRequired.has(f))
  }

  return diffs
})

function acceptChange(type: 'added' | 'modified' | 'removed', field: FieldDiff | ModifiedFieldDiff): void {
  const updatedSchema = JSON.parse(JSON.stringify(props.existing))

  if (type === 'added') {
    updatedSchema.properties[field.name] = (field as FieldDiff).schema
  } else if (type === 'modified') {
    updatedSchema.properties[field.name] = (field as ModifiedFieldDiff).generated
  } else if (type === 'removed') {
    delete updatedSchema.properties[field.name]
  }

  emit('update', updatedSchema)
}

function acceptAllChanges(): void {
  emit('update', props.generated)
}
</script>

<template>
  <div class="schema-compare">
    <header class="compare-header">
      <h3>Schema Comparison</h3>
      <BaseButton v-if="differences.added.length || differences.modified.length || differences.removed.length"
        class="button button--primary" @click="acceptAllChanges">
        Accept All Changes
      </BaseButton>
    </header>

    <div class="differences">
      <!-- Added Fields -->
      <section v-if="differences.added.length" class="diff-section">
        <h4 class="diff-title">
          <Plus class="icon icon--success" />
          New Fields ({{ differences.added.length }})
        </h4>
        <div v-for="field in differences.added" :key="field.name" class="diff-item diff-item--added">
          <div class="diff-content">
            <span class="field-name">{{ field.name }}</span>
            <span class="field-type">{{ field.schema.type }}</span>
            <p v-if="field.schema.description" class="field-description">
              {{ field.schema.description }}
            </p>
          </div>
          <BaseButton class="diff-action" @click="acceptChange('added', field)" title="Accept new field">
            <Check class="icon" />
          </BaseButton>
        </div>
      </section>

      <!-- Modified Fields -->
      <section v-if="differences.modified.length" class="diff-section">
        <h4 class="diff-title">
          <ArrowRight class="icon icon--warning" />
          Modified Fields ({{ differences.modified.length }})
        </h4>
        <div v-for="field in differences.modified" :key="field.name" class="diff-item diff-item--modified">
          <div class="diff-content">
            <span class="field-name">{{ field.name }}</span>
            <div class="field-changes">
              <div class="existing">
                <small>Current:</small>
                <span class="field-type">{{ field.existing.type }}</span>
                <p v-if="field.existing.description" class="field-description">
                  {{ field.existing.description }}
                </p>
              </div>
              <div class="generated">
                <small>Generated:</small>
                <span class="field-type">{{ field.generated.type }}</span>
                <p v-if="field.generated.description" class="field-description">
                  {{ field.generated.description }}
                </p>
              </div>
            </div>
          </div>
          <BaseButton class="diff-action" @click="acceptChange('modified', field)" title="Accept changes">
            <Check class="icon" />
          </BaseButton>
        </div>
      </section>

      <!-- Removed Fields -->
      <section v-if="differences.removed.length" class="diff-section">
        <h4 class="diff-title">
          <Minus class="icon icon--danger" />
          Removed Fields ({{ differences.removed.length }})
        </h4>
        <div v-for="field in differences.removed" :key="field.name" class="diff-item diff-item--removed">
          <div class="diff-content">
            <span class="field-name">{{ field.name }}</span>
            <span class="field-type">{{ field.schema.type }}</span>
            <p v-if="field.schema.description" class="field-description">
              {{ field.schema.description }}
            </p>
          </div>
          <BaseButton class="diff-action" @click="acceptChange('removed', field)" title="Accept removal">
            <Check class="icon" />
          </BaseButton>
        </div>
      </section>

      <!-- Required Field Changes -->
      <section v-if="differences.requiredChanges.added.length || differences.requiredChanges.removed.length"
        class="diff-section">
        <h4 class="diff-title">Required Field Changes</h4>
        <div v-if="differences.requiredChanges.added.length" class="diff-item">
          <div class="diff-content">
            <small>Newly Required:</small>
            <div class="tag-list">
              <span v-for="field in differences.requiredChanges.added" :key="field" class="tag tag--added">
                {{ field }}
              </span>
            </div>
          </div>
        </div>
        <div v-if="differences.requiredChanges.removed.length" class="diff-item">
          <div class="diff-content">
            <small>No Longer Required:</small>
            <div class="tag-list">
              <span v-for="field in differences.requiredChanges.removed" :key="field" class="tag tag--removed">
                {{ field }}
              </span>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
.schema-compare {
  display: grid;
  gap: 1.5rem;
}

.compare-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.differences {
  display: grid;
  gap: 2rem;
}

.diff-section {
  display: grid;
  gap: 1rem;
}

.diff-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: var(--color-text);
  font-size: var(--step-0);
}

.diff-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
}

.diff-content {
  flex: 1;
  min-width: 0;
}

.field-name {
  font-weight: 500;
  color: var(--color-heading);
}

.field-type {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  padding: 0.2em 0.5em;
  background: var(--color-background-mute);
  border-radius: 1em;
  margin-left: 0.5rem;
}

.field-description {
  margin: 0.5rem 0 0;
  font-size: var(--step--1);
  color: var(--color-text-soft);
}

.field-changes {
  display: grid;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.diff-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  background: var(--color-background-soft);
  color: var(--color-text);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.diff-action:hover {
  background: var(--color-accent);
  color: var(--color-background);
}

.icon {
  width: 1.2em;
  height: 1.2em;
}

.icon--success {
  color: var(--color-success);
}

.icon--warning {
  color: var(--color-warning);
}

.icon--danger {
  color: var(--color-danger);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.tag {
  font-size: var(--step--2);
  padding: 0.2em 0.5em;
  border-radius: 0.25rem;
}

.tag--added {
  background: var(--color-success-soft);
  color: var(--color-success);
}

.tag--removed {
  background: var(--color-danger-soft);
  color: var(--color-danger);
}
</style>
