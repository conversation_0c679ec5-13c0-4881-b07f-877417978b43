<script setup lang="ts">
import { computed } from 'vue'
import { Calendar, Check, Key, List, Type } from 'lucide-vue-next'
import type { Schem<PERSON>, <PERSON>hema<PERSON>ield } from '@/types/schema'

type Props = {
  schema: Schema;
  collectionName: string;
};

const props = defineProps<Props>()

const fields = computed(() => {
  const result: Array<SchemaField & { name: string; required: boolean }> = []
  for (const [key, value] of Object.entries(props.schema.properties)) {
    result.push({
      name: key,
      type: value.type,
      description: value.description,
      format: value.format,
      enum: value.enum,
      required: props.schema.required?.includes(key) || false
    })
  }
  return result
})

function getTypeIcon(field: SchemaField) {
  if (field.format === 'date-time') return Calendar
  if (field.type === 'array') return List
  if (field.type === 'boolean') return Check
  if (field.type === 'string') return Type
  return Key
}

function getTypeLabel(field: SchemaField): string {
  if (field.enum) return `enum (${field.enum.length} options)`
  if (field.format === 'date-time') return 'datetime'
  if (field.format === 'email') return 'email'
  if (field.format === 'uri') return 'url'
  return field.type
}
</script>

<template>
  <div class="schema-visualizer">
    <div class="schema-header">
      <h2>{{ collectionName }}</h2>
      <div class="schema-meta">
        <span v-if="schema.required?.length" class="required-count">
          {{ schema.required.length }} required fields
        </span>
        <span class="field-count">
          {{ fields.length }} total fields
        </span>
      </div>
    </div>

    <div class="fields">
      <div v-for="field in fields" :key="field.name" class="field" :class="{ 'field--required': field.required }">
        <div class="field__header">
          <component :is="getTypeIcon(field)" class="icon" />
          <span class="field__name">{{ field.name }}</span>
          <span class="field__type">{{ getTypeLabel(field) }}</span>
          <span v-if="field.required" class="field__required">Required</span>
        </div>
        <div v-if="field.description" class="field__description">
          {{ field.description }}
        </div>
        <div v-if="field.enum" class="field__enum">
          <div class="enum-values">
            <span v-for="value in field.enum" :key="value" class="enum-value">
              {{ value }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.schema-visualizer {
  display: grid;
  gap: 1.5rem;
  padding: 1rem;
}

.schema-header {
  display: flex;
  align-items: baseline;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.schema-header h2 {
  margin: 0;
  color: var(--color-heading);
}

.schema-meta {
  display: flex;
  gap: 1rem;
  font-size: var(--step--1);
  color: var(--color-text-muted);
}

.fields {
  display: grid;
  gap: 1rem;
}

.field {
  padding: 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.field:hover {
  border-color: var(--color-border-hover);
  background: var(--color-background-soft);
}

.field--required {
  border-left: 3px solid var(--color-accent);
}

.field__header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.field__name {
  font-weight: 500;
  color: var(--color-heading);
}

.field__type {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  padding: 0.2em 0.5em;
  background: var(--color-background-mute);
  border-radius: 1em;
}

.field__required {
  font-size: var(--step--2);
  color: var(--color-accent);
  margin-left: auto;
}

.field__description {
  font-size: var(--step--1);
  color: var(--color-text-soft);
  margin-top: 0.5rem;
}

.enum-values {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.enum-value {
  font-size: var(--step--2);
  padding: 0.2em 0.5em;
  background: var(--color-background-mute);
  border-radius: 0.25rem;
  color: var(--color-text-muted);
}

.icon {
  width: 1.2em;
  height: 1.2em;
  color: var(--color-text-muted);
}
</style>
