<script setup lang="ts">
import { Artist } from '@/models/Artist';

defineProps<{
  artist: Artist
}>()
</script>

<template>
  <img v-if="artist.photoUrl" :src="artist.photoUrl" :alt="artist.stageName"
    :title="artist.stageName + ' - ' + artist.unavailabilityReason" />
  <div v-else>{{ (artist?.initials)
    || '?' }}</div>
</template>

<style scoped>
img {
  width: 1em;
  height: 1em;
  border-radius: 50%;
}
</style>
