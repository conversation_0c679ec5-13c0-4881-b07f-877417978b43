<script setup lang="ts">
import {
  Calendar, Building2, Users, Brush,
  FileText, PieChart, AlertTriangle, Wrench,
  Truck, Clock, ClipboardCheck, PiggyBank,
  ChevronsLeft, ChevronsRight
} from 'lucide-vue-next'

type Props = {
  expanded?: boolean
}

withDefaults(defineProps<Props>(), {
  expanded: false
})

defineEmits(['toggle'])
</script>

<template>
  <aside class="sidebar" :class="{ 'sidebar--collapsed': !expanded }">
    <div class="sidebar__content">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <RouterLink to="/events/create" class="action-button">
          <Calendar />
          <span>Create Event</span>
        </RouterLink>
        <RouterLink to="/venues/create" class="action-button">
          <Building2 />
          <span>Add Venue</span>
        </RouterLink>
        <RouterLink to="/artists/create" class="action-button">
          <Users />
          <span>Add Artist</span>
        </RouterLink>
        <RouterLink to="/marketing/materials" class="action-button">
          <Brush />
          <span>Marketing Materials</span>
        </RouterLink>
        <RouterLink to="/contracts" class="action-button">
          <FileText />
          <span>Manage Contracts</span>
        </RouterLink>
        <RouterLink to="/reports" class="action-button">
          <PieChart />
          <span>Financial Reports</span>
        </RouterLink>
        <RouterLink to="/calendar/conflicts" class="action-button">
          <AlertTriangle />
          <span>Check Conflicts</span>
        </RouterLink>
        <RouterLink to="/venues/tech-specs" class="action-button">
          <Wrench />
          <span>Tech Specs</span>
        </RouterLink>
        <RouterLink to="/venues/load-in-guide" class="action-button">
          <Truck />
          <span>Load-in Guide</span>
        </RouterLink>
        <RouterLink to="/shows/timings" class="action-button">
          <Clock />
          <span>Show Timings</span>
        </RouterLink>
        <RouterLink to="/shows/setup-checklist" class="action-button">
          <ClipboardCheck />
          <span>Setup Checklist</span>
        </RouterLink>
        <RouterLink to="/finance/deposits" class="action-button">
          <PiggyBank />
          <span>Deposit Tracker</span>
        </RouterLink>
      </div>
    </div>
    <BaseButton class="sidebar__toggle" @click="$emit('toggle')" aria-label="Toggle sidebar">
      <component :is="expanded ? ChevronsLeft : ChevronsRight" />
    </BaseButton>
  </aside>
</template>

<style scoped>
.sidebar {
  position: fixed;
  top: 4rem;
  bottom: 0;
  left: 0;
  width: 300px;
  background: var(--color-background-soft);
  border-right: 1px solid var(--color-border);
  transform: translateX(-300px);
  transition: all 0.3s ease;
  z-index: 100;
  box-shadow: var(--shadow-lg);
}

.sidebar--collapsed {
  transform: translateX(-300px);
}

.sidebar:not(.sidebar--collapsed) {
  transform: translateX(0);
}

.sidebar__content {
  height: 100%;
  padding: 1.5rem;
  overflow-y: auto;
}

.sidebar__toggle {
  position: absolute;
  top: 1rem;
  right: -2.5rem;
  width: 2.5rem;
  height: 4rem;
  background: var(--color-brand);
  color: var(--color-background);
  border: none;
  border-radius: 0 0.5rem 0.5rem 0;
  cursor: pointer;
  display: grid;
  place-items: center;
  transition: all 0.2s ease;
}

.sidebar__toggle:hover {
  background: var(--color-brand-dark);
  transform: scale(1.05);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  width: 100%;
  white-space: nowrap;
  font-size: var(--step--1);
}

.action-button svg {
  width: 1.5em;
  height: 1.5em;
  flex-shrink: 0;
  color: var(--color-brand-muted);
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--color-brand-soft);
  border-color: var(--color-brand);
  transform: translateX(4px);
}

.action-button:hover svg {
  color: var(--color-brand);
}

h3 {
  margin-bottom: 1rem;
  color: var(--color-text-muted);
  font-size: var(--step-0);
  font-weight: 500;
}

/* Scrollbar styling */
.sidebar__content {
  scrollbar-width: thin;
  scrollbar-color: var(--color-brand-muted) var(--color-background-soft);
}

.sidebar__content::-webkit-scrollbar {
  width: 6px;
}

.sidebar__content::-webkit-scrollbar-track {
  background: var(--color-background-soft);
}

.sidebar__content::-webkit-scrollbar-thumb {
  background-color: var(--color-brand-muted);
  border-radius: 3px;
}
</style>
