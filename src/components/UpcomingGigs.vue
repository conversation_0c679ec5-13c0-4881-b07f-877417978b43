<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import ActBadgeLogo from './ActBadgeLogo.vue'
import { useEvents } from '../composables/useEvents'
import { Event } from '../models/Event'
import DateRangeSelector from './DateRangeSelector.vue'

const { events, isLoading: isLoadingEvents, subscribeToEvents } = useEvents()

// Date range state
const fromDate = ref(new Date()) // Default filter date (today)
const selectedFromDate = ref<Date | null>(null) // Explicitly selected start date
const selectedToDate = ref<Date | null>(null) // Explicitly selected end date

// State for year selection
const selectedYear = ref<string>('')

// Watch for year selection changes
watch(selectedYear, (newYear) => {
  if (newYear) {
    const year = parseInt(newYear)

    if (typeof year !== 'number') {
      console.error('Invalid year:', year)
      return
    }

    const start = new Date(year, 0, 1)
    start.setHours(0, 0, 0, 0)  // Ensure start of day

    const end = new Date(year, 11, 31)
    end.setHours(23, 59, 59, 999)  // Ensure end of day

    selectedFromDate.value = start
    selectedToDate.value = end
  }
})

// Watch for date changes to clear year selection when dates are cleared
watch([selectedFromDate, selectedToDate], async ([newFromDate, newToDate]) => {
  // If we're clearing the dates
  if (!newFromDate && !newToDate) {
    selectedYear.value = ''
  }

  await initializeEvents()
})

// State for filtered acts
const filteredActIds = ref(new Set<string>())

// Computed properties
const totalGigs = computed(() => {
  return events.value.filter(event => {
    // Only include events within date range and that have at least one unfiltered act
    return isEventInDateRange(event as Event) && event.acts.some(actId => !filteredActIds.value.has(actId))
  }).length
})

const gigsByAct = computed(() => {
  const counts: Record<string, number> = {}

  events.value.forEach(event => {
    // Only count events within date range where at least one act is not filtered
    if (isEventInDateRange(event as Event) && event.acts.some(actId => !filteredActIds.value.has(actId))) {
      event.acts.forEach(actId => {
        if (!filteredActIds.value.has(actId)) {
          counts[actId] = (counts[actId] || 0) + 1
        }
      })
    }
  })

  return counts
})

const uniqueActs = computed(() => {
  const actSet = new Set<string>()

  events.value.forEach(event => {
    // Only include acts from events within date range
    if (isEventInDateRange(event as Event)) {
      event.acts.forEach(actId => actSet.add(actId))
    }
  })

  return Array.from(actSet)
    .sort((a, b) => a.localeCompare(b))
})

const sharedGigs = computed(() => {
  // Get events with multiple acts within date range
  const multiActEvents = events.value.filter((event) =>
    isEventInDateRange(event as Event) && event.acts.length > 1
  )
  if (multiActEvents.length === 0) return null

  // Get all acts involved in shared gigs
  const sharedActsSet = new Set<string>()
  multiActEvents.forEach(event => {
    event.acts.forEach(actId => sharedActsSet.add(actId))
  })

  // Convert to array and sort
  const sharedActs = Array.from(sharedActsSet)
    .map(actId => ({
      actId,
      count: gigsByAct.value[actId] || 0
    }))

  // Calculate all intersections with sorted act IDs
  const intersections: Record<string, number> = {}
  sharedActs.forEach((act1, i) => {
    sharedActs.slice(i + 1).forEach(act2 => {
      const key = `${act1.actId}_${act2.actId}`
      intersections[key] = multiActEvents.filter(event =>
        event.acts.includes(act1.actId) && event.acts.includes(act2.actId)
      ).length
    })
  })

  return {
    acts: sharedActs,
    count: multiActEvents.length,
    intersections
  }
})

const computedFromDate = computed(() => selectedFromDate.value || fromDate.value)

// Helper function to check if event is within date range
function isEventInDateRange(event: Event): boolean {
  const eventDate = event.when.toDate()

  if (eventDate < computedFromDate.value || (selectedToDate.value && eventDate > selectedToDate.value)) {
    return false
  }

  return true
}

// Methods
const toggleActFilter = (actId: string) => {
  if (filteredActIds.value.has(actId)) {
    filteredActIds.value.delete(actId)
  } else {
    filteredActIds.value.add(actId)
  }
}

const isActFiltered = (actId: string) => {
  return filteredActIds.value.has(actId)
}

// Initialize data
async function initializeEvents() {
  try {
    await subscribeToEvents({
      fromDate: computedFromDate.value,
      toDate: selectedToDate.value ?? undefined,
      includePaid: true
    })
  } catch (err) {
    console.error('Failed to initialize events:', err)
  }
}

onMounted(async () => {
  await initializeEvents()
})
</script>

<template>
  <div class="upcoming-gigs" :class="{ 'is-loading': isLoadingEvents }">
    <div class="date-controls">
      <div class="selectors-container">
        <DateRangeSelector v-model:fromDate="selectedFromDate" v-model:toDate="selectedToDate"
          v-model:selectedYear="selectedYear" :defaultFromDate="fromDate" :showSummary="true" showYearSelector />
      </div>
    </div>

    <Transition name="fade" mode="out-in">
      <div v-if="!isLoadingEvents" key="content" class="acts-section">
        <div class="acts-grid">
          <div v-for="actId in uniqueActs" :key="actId" class="act-filter-item shadow"
            :class="{ 'filtered': isActFiltered(actId) }" @click="toggleActFilter(actId)">
            <ActBadgeLogo :actId="actId" size="1.8em" class="act-logo" />
            <span class="gig-count">{{ gigsByAct[actId] || 0 }}</span>
          </div>
        </div>

        <div class="shared-gigs-container">
          <div v-if="totalGigs"><span class="highlight">{{ totalGigs }}</span> total</div>
          <div v-else><small>No upcoming gigs for selected acts.</small></div>
          <div v-if="sharedGigs" class="shared-gigs-header">
            <div class="shared-count">
              <span class="highlight">{{ sharedGigs.count }}</span> shared
            </div>
            <div class="acts-breakdown">
              <div v-for="(act, index) in sharedGigs.acts" :key="act.actId" class="act-detail"
                :style="{ marginLeft: index > 0 ? '-0.75rem' : '0' }">
                <ActBadgeLogo :actId="act.actId" size="1.5em" class="act-logo" />
              </div>
            </div>
          </div>
          <div v-else>
            <small>Selected acts have no shared gigs.</small>
          </div>
        </div>
      </div>
      <div v-else key="loading" class="loading-overlay">
        <span class="loading-text">Loading...</span>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.date-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.selectors-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
  padding: var(--space-s);
  background: var(--color-bg-3);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
}

.selectors-row {
  display: flex;
  gap: var(--space-s);
  align-items: center;
}

.selectors-row :deep(.select-wrapper) {
  min-width: 150px;
}

.date-range-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: var(--space-xs);
  border-top: 1px solid var(--color-border-soft);
}

.date-range-row :deep(.date-range-summary) {
  margin: 0;
}

.upcoming-gigs {
  min-height: 200px;
  display: grid;
  gap: var(--space-m);
}

.is-loading {
  pointer-events: none;
  opacity: 0.7;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-3);
  border-radius: var(--radius-m);
}

.loading-text {
  color: var(--color-text-light);
}

.total-count {
  font-size: var(--step-1);
  font-weight: 600;
  color: var(--color-text);
}

.shared-gigs-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-m);
}

.shared-count {
  display: flex;
  align-items: baseline;
  gap: var(--space-xs);
  white-space: nowrap;
}

.count-number {
  font-size: var(--step-2);
  font-weight: bold;
  color: var(--color-heading);
}

.count-label {
  font-size: var(--step--1);
  color: var(--color-text-light);
  font-weight: 500;
}

.acts-breakdown {
  display: flex;
  flex-wrap: nowrap;
  gap: 0;
  flex: 1;
}

.act-detail {
  display: flex;
  align-items: center;
  padding: var(--space-2xs);
  z-index: 1;
  transition: transform var(--transition-out);
}

.act-detail:hover {
  transform: translateY(-2px);
  z-index: 2;
}

.acts-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  width: 100%;
}

.acts-filter {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.acts-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 50px), 1fr));
  gap: var(--space-s);
  justify-content: center;
}

.act-filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  background-color: var(--color-bg-3);
  border-radius: var(--radius-m);
  cursor: pointer;
  transition: all var(--transition-out);
  min-width: 4rem;
  border: 1px solid var(--color-border);
  padding: var(--space-xs);
}

.act-filter-item:hover {
  transform: translateY(-2px);
  background: var(--color-background-soft);
}

.act-filter-item.filtered {
  opacity: 0.5;
  background: var(--color-background-mute);
}

.gig-count {
  font-weight: 600;
  color: var(--color-text);
  font-size: var(--step-1);
  line-height: 1;
}

.loading {
  color: var(--color-text-light);
  font-style: italic;
}

.highlight {
  color: var(--color-light);
  font-weight: 600;
}

.act-logo {
  border-radius: 100%;
  filter: drop-shadow(0 0 2px black);
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
