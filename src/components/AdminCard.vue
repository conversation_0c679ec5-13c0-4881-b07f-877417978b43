<script setup lang="ts">
import EntityActions from '@/components/base/EntityActions.vue'
import { useEntityCard } from '@/composables/useEntityCard'

export type Section = {
  id: string
  icon: string
  title: string
  description: string
  route: string
  stats: {
    [key: string]: string
  }
}

const props = defineProps<{
  section: Section
}>()

const {
  isLoading,
  error,
  viewEntity,
  editEntity,
  deleteEntity
} = useEntityCard({
  entityType: 'section',
  entity: props.section
})
</script>

<template>
  <BaseCard class="admin-card">
    <template #header>
      <div class="admin-card__icon">
        {{ section.icon }}
      </div>
      <h2>{{ section.title }}</h2>
    </template>

    <div class="admin-card__content">
      <p>{{ section.description }}</p>
      <div class="admin-card__stats">
        <div v-for="(value, key) in section.stats" :key="key" class="stat">
          <span class="stat__value">{{ value }}</span>
          <span class="stat__label">{{ key }}</span>
        </div>
      </div>
      <p v-if="error" class="error-message">{{ error }}</p>
    </div>

    <template #footer>
      <EntityActions :is-loading="isLoading" @view="viewEntity" @edit="editEntity" @delete="deleteEntity" />
      <RouterLink :to="section.route" class="action-button">
        View All
      </RouterLink>
      <RouterLink :to="`${section.route}/new`" class="action-button action-button--accent">
        Add New
      </RouterLink>
    </template>
  </BaseCard>
</template>

<style scoped>
.admin-card {
  container-type: inline-size;
}

.admin-card__icon {
  font-size: var(--step-3);
  color: var(--color-brand);
  width: 1.2em;
  height: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-mute);
  border-radius: 0.3em;
}

.admin-card__content {
  display: grid;
  gap: 0.5rem;
  text-align: center;
}

.admin-card__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background-color: var(--color-background-mute);
  border-radius: 0.5rem;
}

.stat {
  text-align: center;
}

.stat__value {
  display: block;
  font-size: var(--step-2);
  font-weight: bold;
  color: var(--color-accent);
}

.stat__label {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  text-transform: capitalize;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}

.action-button {
  flex: 1;
  padding: 0.8em 1.2em;
  border-radius: 0.5em;
  text-decoration: none;
  text-align: center;
  color: var(--color-brand);
  background-color: var(--color-background-mute);
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--color-brand);
  color: var(--color-background);
}

.action-button--accent {
  background-color: var(--color-brand);
  color: var(--color-background);
}

.action-button--accent:hover {
  filter: brightness(1.1);
}
</style>
