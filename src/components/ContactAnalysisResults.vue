<script setup lang="ts">
import { ExternalLink } from 'lucide-vue-next'

type AnalysisResults = {
  analysis: {
    filename: string
    entities: {
      name: { value: string }[]
      email: { value: string }[]
      phone: { value: string }[]
      contact: { value: string; type?: string; confidence: number }[]
    }
    venueDetails: Record<string, string>
    chronology: {
      events: {
        date: string
        content: string
        metadata?: {
          booking?: {
            fee?: number
            time?: string
            status?: string
          }
        }
      }[]
    }
    context: {
      venueSubtype?: string
      probableType?: string
    }
  }[]
}

defineProps<{
  analysisResults: AnalysisResults
}>()
</script>

<template>
  <div class="analysis-results">
    <div v-for="(result, index) in analysisResults.analysis" :key="index" class="result-card">
      <header class="result-header">
        <div class="result-title">
          <h4>{{ result.filename }}</h4>
          <router-link v-if="result.entities.name" :to="`/venues/${encodeURIComponent(result.entities.name[0].value)}`"
            class="venue-link">
            View Venue
            <ExternalLink class="icon" />
          </router-link>
        </div>
        <span class="venue-type">{{ result.context.venueSubtype || result.context.probableType }}</span>
      </header>

      <div class="result-body">
        <!-- Basic Information -->
        <section class="info-section">
          <h5>Basic Information</h5>
          <div class="info-grid">
            <div v-if="result.entities.name">
              <strong>Name:</strong> {{ result.entities.name[0].value }}
            </div>
            <div v-if="result.entities.email">
              <strong>Email:</strong>
              <ul>
                <li v-for="email in result.entities.email" :key="email.value">
                  {{ email.value }}
                </li>
              </ul>
            </div>
            <div v-if="result.entities.phone">
              <strong>Phone:</strong>
              <ul>
                <li v-for="phone in result.entities.phone" :key="phone.value">
                  {{ phone.value }}
                </li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Contact Persons -->
        <section class="info-section" v-if="result.entities.contact">
          <h4>Contact Persons</h4>
          <div v-for="contact in result.entities.contact" :key="contact.value" class="contact-person">
            <div class="contact-header">
              <strong>{{ contact.value }}</strong>
              <span class="confidence-score">
                Confidence: {{ Math.round(contact.confidence * 100) }}%
              </span>
            </div>
            <div v-if="contact.type" class="contact-role">{{ contact.type }}</div>
          </div>
        </section>

        <!-- Chronological History -->
        <section class="info-section" v-if="result.chronology?.events?.length">
          <h4>History</h4>
          <div class="timeline">
            <div v-for="event in result.chronology.events" :key="event.date" class="timeline-event">
              <div class="event-date">
                {{ new Date(event.date).toLocaleDateString() }}
              </div>
              <div class="event-content">
                <div class="event-text">{{ event.content }}</div>
                <div v-if="event.metadata?.booking" class="event-metadata">
                  <span v-if="event.metadata.booking.fee">
                    Fee: £{{ event.metadata.booking.fee }}
                  </span>
                  <span v-if="event.metadata.booking.time">
                    Time: {{ event.metadata.booking.time }}
                  </span>
                  <span v-if="event.metadata.booking.status" class="booking-status">
                    {{ event.metadata.booking.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Venue Details -->
        <section class="info-section" v-if="result.venueDetails">
          <h4>Venue Details</h4>
          <div class="info-grid">
            <div v-for="(value, key) in result.venueDetails" :key="key">
              <strong>{{ key }}:</strong> {{ value }}
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.analysis-results {
  display: grid;
  gap: var(--space-step--2);
  font-size: var(--step--3);
}

.result-card {
  background: var(--color-background);
  border-radius: var(--br-step-0);
  padding: var(--pad-step--2);
  box-shadow: var(--shadow-step-1);
}

.result-title {
  display: flex;
  align-items: center;
  gap: var(--space-step-0);
}

.result-title h4 {
  font-size: var(--step--2);
  margin: 0;
}

.venue-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-step--3);
  font-size: var(--step--4);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--pad-step--3) var(--pad-step--2);
  background: var(--color-primary-soft);
  border-radius: var(--br-step--2);
}

.venue-link:hover {
  background: var(--color-primary);
  color: var(--color-background);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-step--1);
}

.venue-type {
  font-size: var(--step--4);
  padding: var(--pad-step--3) var(--pad-step--2);
  background: var(--color-primary-soft);
  color: var(--color-primary);
  border-radius: var(--br-step--2);
}

.info-section {
  margin-top: var(--space-step--3);
  padding-top: var(--space-step--3);
  border-top: 1px solid var(--color-border);
}

.info-section h5 {
  margin-bottom: var(--space-step--4);
  font-size: var(--step--3);
}

.info-grid {
  display: grid;
  gap: var(--space-step--3);
}

.timeline {
  display: grid;
  gap: var(--space-step--1);
}

.timeline-event {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--space-step--3);
  padding: var(--pad-step--3);
  background: var(--color-background-soft);
  border-radius: var(--br-step--1);
}

.event-date {
  font-size: var(--step--3);
  color: var(--color-text-soft);
}

.event-metadata {
  display: flex;
  gap: var(--space-step-0);
  margin-top: var(--space-step--4);
  font-size: var(--step--3);
}

.booking-status {
  text-transform: capitalize;
  padding: var(--pad-step--3) var(--pad-step--2);
  background: var(--color-success-soft);
  color: var(--color-success);
  border-radius: var(--br-step--2);
}

.contact-person {
  padding: var(--pad-step--2);
  background: var(--color-background-soft);
  border-radius: var(--br-step--1);
  margin-bottom: var(--space-step--2);
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.confidence-score {
  font-size: var(--step--3);
  color: var(--color-text-soft);
}

.contact-role {
  font-size: var(--step--2);
  color: var(--color-text-soft);
  margin-top: var(--space-step--3);
}

/* Add responsive styles */
@media (max-width: 768px) {
  .result-card {
    padding: var(--pad-step--1);
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-step--2);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
