<script setup lang="ts">
import { ref, computed } from 'vue'
import { generateCollectionSchema } from '@/utils/schemaGenerator.ts'
import { RefreshCw, Download, AlertCircle, Code } from 'lucide-vue-next'
import SchemaVisualizer from '@/components/schema/SchemaVisualizer.vue'
import SchemaCompare from '@/components/schema/SchemaCompare.vue'
import { loadSchema } from '@/utils/schemaLoader.ts'
import { saveSchema } from '@/services/schemaService'
import { useToast } from 'vue-toastification'
import type { Schema } from '@/types/schema'

type Collection = {
  id: string
  name: string
}

const collections: Collection[] = [
  { id: 'acts', name: 'Acts' },
  { id: 'agents', name: 'Agents' },
  { id: 'artistAvailability', name: 'Artist Availability' },
  { id: 'artists', name: 'Artists' },
  { id: 'emails', name: 'Emails' },
  { id: 'events', name: 'Events' },
  { id: 'eventNotes', name: 'Event Notes' },
  { id: 'repertoire', name: 'Repertoire' },
  { id: 'repertoireNotes', name: 'Repertoire Notes' },
  { id: 'tasks', name: 'Tasks' },
  { id: 'testimonials', name: 'Testimonials' },
  { id: 'users', name: 'Users' },
  { id: 'venues', name: 'Venues' },
  { id: 'venueNotes', name: 'Venue Notes' }
].sort((a, b) => a.name.localeCompare(b.name))

const selectedCollection = ref('')
const isAnalyzing = ref(false)
const generatedSchema = ref<Schema | null>(null)
const error = ref<string | null>(null)
const showJson = ref(true)
const existingSchema = ref<Schema | null>(null)

const formattedSchema = computed(() => {
  if (!generatedSchema.value) return ''
  return JSON.stringify(generatedSchema.value, null, 2)
})

const selectedCollectionName = computed(() => {
  return collections.find(c => c.id === selectedCollection.value)?.name || ''
})

function loadExistingSchema(): void {
  const schema = loadSchema(selectedCollection.value)
  if (schema &&
    '$schema' in schema &&
    'type' in schema &&
    'properties' in schema &&
    'required' in schema) {
    // First cast to unknown, then to Schema to avoid direct conversion error
    existingSchema.value = schema as unknown as Schema
  } else {
    existingSchema.value = null
  }
}

async function analyzeCollection(): Promise<void> {
  if (!selectedCollection.value) return

  try {
    isAnalyzing.value = true
    error.value = null
    loadExistingSchema()
    generatedSchema.value = await generateCollectionSchema(selectedCollection.value)
  } catch (e) {
    console.error('Schema analysis error:', e)
    error.value = e instanceof Error ? e.message : 'An unknown error occurred'
  } finally {
    isAnalyzing.value = false
  }
}

function downloadSchema(): void {
  if (!generatedSchema.value) return

  const blob = new Blob([formattedSchema.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${selectedCollection.value}.schema.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

function toggleJsonView(): void {
  showJson.value = !showJson.value
}

function compareWithExisting(): void {
  // TODO: Add functionality to compare generated schema with existing schema file
}

async function updateSchema(schema: Schema): Promise<void> {
  if (!schema.properties) {
    throw new Error('Schema must have properties')
  }
  try {
    await saveSchema(selectedCollection.value, schema)
    existingSchema.value = schema
    const toast = useToast()
    toast.success('Schema updated successfully')
  } catch (error) {
    console.error('Failed to save schema:', error)
    const toast = useToast()
    toast.error('Failed to save schema: ' + (error instanceof Error ? error.message : 'An unknown error occurred'))
  }
}
</script>

<template>
  <div class="schema-analyzer">
    <div class="schema-analyzer__toolbar">
      <div class="schema-analyzer__controls">
        <div class="select-wrapper">
          <select v-model="selectedCollection" :disabled="isAnalyzing" class="base-select">
            <option value="">Select collection...</option>
            <option v-for="collection in collections" :key="collection.id" :value="collection.id">
              {{ collection.name }}
            </option>
          </select>
        </div>

        <div class="button-group">
          <BaseButton size="compact" icon purpose="primary" @click="analyzeCollection"
            :disabled="!selectedCollection || isAnalyzing" :title="isAnalyzing ? 'Analyzing...' : 'Analyze Schema'">
            <RefreshCw class="icon" :class="{ 'icon--spinning': isAnalyzing }" />
          </BaseButton>

          <BaseButton v-if="generatedSchema" size="compact" icon purpose="secondary" @click="downloadSchema"
            title="Download Schema">
            <Download class="icon" />
          </BaseButton>

          <BaseButton v-if="generatedSchema" size="compact" icon purpose="secondary" @click="toggleJsonView"
            :title="showJson ? 'Hide JSON' : 'Show JSON'">
            <Code class="icon" :class="{ 'icon--active': showJson }" />
          </BaseButton>
        </div>
      </div>

      <div v-if="error" class="error-message">
        <AlertCircle class="icon" />
        <span>{{ error }}</span>
      </div>
    </div>

    <div v-if="generatedSchema" class="schema-content" :class="{ 'with-json': showJson }">
      <div class="schema-visual">
        <SchemaVisualizer :schema="generatedSchema" :collection-name="selectedCollectionName" />

        <div v-if="existingSchema" class="schema-comparison">
          <SchemaCompare :generated="generatedSchema" :existing="existingSchema" @update="updateSchema" />
        </div>
      </div>

      <Transition name="slide">
        <div v-if="showJson" class="schema-json">
          <div class="schema-result">
            <pre><code>{{ formattedSchema }}</code></pre>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.schema-analyzer {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
}

.base-select {
  background-color: var(--color-background-mute);
}

.schema-analyzer__toolbar {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--color-background-soft);
  border-radius: var(--radius-m);
  padding: 1rem;
  display: grid;
  gap: 1rem;
}

.schema-analyzer__controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.schema-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  overflow: hidden;
  position: relative;
  min-height: 0;
  padding: 1rem;
}

.schema-content.with-json {
  grid-template-columns: 1fr 400px;
}

.schema-visual {
  overflow: auto;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
}

.schema-json {
  overflow: hidden;
  border-left: 1px solid var(--color-border);
  background: var(--color-background-soft);
  border-radius: 0.5rem;
}

.schema-result {
  height: 100%;
  padding: 0.75rem;
  overflow: auto;
}

.select-wrapper {
  flex: 1;
}

select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.button-group {
  display: flex;
  gap: 0.25rem;
}

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.button--secondary {
  background: var(--color-background);
  color: var(--color-text);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon {
  width: 1.2em;
  height: 1.2em;
}

.icon--spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--color-danger-soft);
  color: var(--color-danger);
  border-radius: 0.5rem;
  font-size: var(--step--1);
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.icon--active {
  color: var(--color-accent);
}

.schema-comparison {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--color-border);
}
</style>
