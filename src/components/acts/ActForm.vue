<script setup lang="ts">
import { ref } from 'vue'
import type { LogoUrls } from '@/models/Act'
import { CloudUpload } from 'lucide-vue-next'
import { useCloudinary } from '@/composables/useCloudinary'
import { Act } from '@/models/Act'
const props = defineProps<{
  initialValues?: Partial<Act>
}>()

const emit = defineEmits<{
  (e: 'submit', data: Act): void
}>()

const { isUploading, uploadError, uploadImage } = useCloudinary()

// Initialize default logo URLs
const defaultLogoUrls: LogoUrls = {
  text: '',
  badge: ''
}

const formData = ref<Partial<Act>>({
  name: props.initialValues?.name || '',
  description: props.initialValues?.description || '',
  defaultGigDescription: props.initialValues?.defaultGigDescription || '',
  displayName: props.initialValues?.displayName || '',
  logoUrls: props.initialValues?.logoUrls || defaultLogoUrls,
  photoUrl: props.initialValues?.photoUrl || '',
  website: props.initialValues?.website || '',
  artistIds: props.initialValues?.artistIds || []
})

const logoTextFile = ref<File | null>(null)
const logoBadgeFile = ref<File | null>(null)
const photoFile = ref<File | null>(null)

async function handleLogoTextUpload(event: Event): Promise<void> {
  const input = event.target as HTMLInputElement
  const files = input.files
  if (!files?.length) return

  logoTextFile.value = files[0]
  try {
    const url = await uploadImage(logoTextFile.value, 'act-logos')
    formData.value.logoUrls = {
      ...formData.value.logoUrls || defaultLogoUrls,
      text: url
    }
  } catch (error) {
    console.error('Failed to upload logo text:', error)
  }
}

async function handleLogoBadgeUpload(event: Event): Promise<void> {
  const input = event.target as HTMLInputElement
  const files = input.files
  if (!files?.length) return

  logoBadgeFile.value = files[0]
  try {
    const url = await uploadImage(logoBadgeFile.value, 'act-logos')
    formData.value.logoUrls = {
      ...formData.value.logoUrls || defaultLogoUrls,
      badge: url
    }
  } catch (error) {
    console.error('Failed to upload logo badge:', error)
  }
}

async function handlePhotoUpload(event: Event): Promise<void> {
  const input = event.target as HTMLInputElement
  const files = input.files
  if (!files?.length) return

  photoFile.value = files[0]
  try {
    const url = await uploadImage(photoFile.value, 'act-photos')
    formData.value.photoUrl = url
  } catch (error) {
    console.error('Failed to upload photo:', error)
  }
}

function handleSubmit(): void {
  emit('submit', formData.value as Act)
}
</script>

<template>
  <form @submit.prevent="handleSubmit" class="act-form">
    <div class="form-group">
      <label for="name">Name</label>
      <BaseInput id="name" v-model="formData.name" required />
    </div>

    <div class="form-group">
      <label for="displayName">Display Name</label>
      <BaseInput id="displayName" v-model="formData.displayName" required />
    </div>

    <div class="form-group">
      <label for="description">Description</label>
      <BaseEditor id="description" placeholder="Enter description..." v-model="formData.description" required />
    </div>

    <div class="form-group">
      <label for="defaultGigDescription">Default Gig Description</label>
      <BaseEditor id="defaultGigDescription" placeholder="Enter default gig description..."
        v-model="formData.defaultGigDescription" required />
    </div>

    <div class="form-group">
      <label for="website">Website</label>
      <BaseInput type="url" id="website" v-model="formData.website" placeholder="https://" />
    </div>

    <div class="form-group">
      <label>Logo Text</label>
      <div class="image-upload">
        <input type="file" accept="image/*" @change="handleLogoTextUpload" class="file-input" />
        <BaseButton type="button" class="upload-button" :disabled="isUploading">
          <CloudUpload class="icon" />
          {{ (formData.logoUrls || defaultLogoUrls).text ? 'Change Logo Text' : 'Upload Logo Text' }}
        </BaseButton>
        <!-- Logo Text Preview -->
        <img v-if="formData.logoUrls?.text" :src="formData.logoUrls.text" alt="Logo Text" class="preview" />
      </div>
    </div>

    <div class="form-group">
      <label>Logo Badge</label>
      <div class="image-upload">
        <input type="file" accept="image/*" @change="handleLogoBadgeUpload" class="file-input" />
        <BaseButton type="button" class="upload-button" :disabled="isUploading">
          <CloudUpload class="icon" />
          {{ (formData.logoUrls || defaultLogoUrls).badge ? 'Change Logo Badge' : 'Upload Logo Badge' }}
        </BaseButton>
        <!-- Logo Badge Preview -->
        <img v-if="formData.logoUrls?.badge" :src="formData.logoUrls.badge" alt="Logo Badge" class="preview" />
      </div>
    </div>

    <div class="form-group">
      <label>Photo</label>
      <div class="image-upload">
        <input type="file" accept="image/*" @change="handlePhotoUpload" class="file-input" />
        <BaseButton type="button" class="upload-button" :disabled="isUploading">
          <CloudUpload class="icon" />
          {{ formData.photoUrl ? 'Change Photo' : 'Upload Photo' }}
        </BaseButton>
        <img v-if="formData.photoUrl" :src="formData.photoUrl" alt="Act Photo" class="preview" />
      </div>
    </div>

    <p v-if="uploadError" class="error-message">{{ uploadError }}</p>

    <div class="form-actions">
      <BaseButton type="submit" class="submit-button">
        {{ props.initialValues ? 'Update Act' : 'Create Act' }}
      </BaseButton>
    </div>
  </form>
</template>

<style scoped>
.act-form {
  max-width: var(--form-width, 800px);
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  box-shadow: var(--shadow);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
}

.image-upload {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-input {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 1;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 150px;
}

.icon {
  width: 1.2em;
  height: 1.2em;
}

.preview {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.error-message {
  color: var(--color-error);
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .act-form {
    padding: 1rem;
  }

  .image-upload {
    flex-direction: column;
    align-items: flex-start;
  }

  .preview {
    margin-top: 0.5rem;
  }
}
</style>
