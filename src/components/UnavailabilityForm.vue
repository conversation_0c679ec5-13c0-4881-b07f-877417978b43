<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useArtistAvailability } from '@/composables/useArtistAvailability'
import { useArtists } from '@/composables/useArtists'
import { Unavailability } from '@/models/Unavailability'

type Props = {
  dates: Date[]
  editingUnavailability: Unavailability | null
}

const props = withDefaults(defineProps<Props>(), {
  editingUnavailability: null
})

const emit = defineEmits<{
  (e: 'close'): void
}>()

const { artists } = useArtists()
const { markUnavailable, updateUnavailability } = useArtistAvailability()

const selectedArtist = ref<string>('')

// Sort dates to find earliest and latest
const sortedDates = computed<Date[]>(() => {
  return [...props.dates].sort((a, b) => a.getTime() - b.getTime())
})

// Helper to ensure date is properly set to midnight UTC
function normalizeDate(date: Date): Date {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0)
  return d
}

// Helper function to format date for input
function formatDateForInput(date: Date): string {
  const d = normalizeDate(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const startDate = computed<Date>(() => normalizeDate(sortedDates.value[0]))
const lastSelectedDate = computed<Date>(() => normalizeDate(sortedDates.value[sortedDates.value.length - 1]))
const endDate = ref<string>(formatDateForInput(lastSelectedDate.value))

// Add computed for date display
const dateRangeText = computed<string>(() => {
  if (props.dates.length === 1) {
    return startDate.value.toLocaleDateString()
  }
  return `${props.dates.length} days selected (${startDate.value.toLocaleDateString()} - ${lastSelectedDate.value.toLocaleDateString()})`
})

const reason = ref<string>('')

// Pre-fill form when editing
onMounted((): void => {
  if (props.editingUnavailability) {
    selectedArtist.value = props.editingUnavailability.artistId
    endDate.value = formatDateForInput(props.editingUnavailability.endDate instanceof Date
      ? props.editingUnavailability.endDate
      : new Date(props.editingUnavailability.endDate))
    reason.value = props.editingUnavailability.reason || ''
  }
})

const handleSubmit = async (): Promise<void> => {
  if (!selectedArtist.value) return

  try {
    const normalizedStartDate = normalizeDate(sortedDates.value[0])
    const normalizedEndDate = normalizeDate(new Date(endDate.value))

    if (props.editingUnavailability?.id) {
      // Update existing unavailability
      await updateUnavailability(
        props.editingUnavailability.id,
        selectedArtist.value,
        normalizedStartDate,
        normalizedEndDate,
        reason.value
      )
    } else {
      // Create new unavailability
      await markUnavailable(
        selectedArtist.value,
        normalizedStartDate,
        normalizedEndDate,
        reason.value
      )
    }
    emit('close')
  } catch (error) {
    console.error('Error saving unavailability:', error)
  }
}
</script>

<template>
  <div class="unavailability-form" @click.stop>
    <h3>{{ props.editingUnavailability ? 'Edit Unavailability' : 'Mark Artist as Unavailable' }}</h3>

    <form @submit.prevent="handleSubmit">
      <div class="form-group">
        <label for="artist">Artist</label>
        <select id="artist" v-model="selectedArtist" required>
          <option value="">Select an artist</option>
          <option v-for="(artist, index) in artists" :key="artist.id ?? index" :value="artist.id">
            {{ artist.stageName }} ({{ artist.firstName }} {{ artist.lastName }})
          </option>
        </select>
      </div>

      <div class="form-group">
        <label>Selected Dates</label>
        <div class="selected-dates">
          {{ dateRangeText }}
        </div>
      </div>

      <div class="form-group">
        <label for="endDate">End Date</label>
        <input type="date" id="endDate" v-model="endDate" :min="formatDateForInput(startDate)" required />
      </div>

      <div class="form-group">
        <label for="reason">Reason (optional)</label>
        <textarea id="reason" v-model="reason" rows="3"></textarea>
      </div>

      <div class="form-actions">
        <BaseButton type="button" class="cancel-button" @click="emit('close')">
          Cancel
        </BaseButton>
        <BaseButton purpose="primary" type="submit" class="submit-button">
          Save
        </BaseButton>
      </div>
    </form>
  </div>
</template>

<style scoped>
.unavailability-form {
  background-color: var(--color-background);
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 90%;
  max-width: 400px;
  box-shadow: var(--shadow);
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

select,
input,
textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-background-soft);
  color: var(--color-text);
  font-size: var(--step--1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.submit-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--step--1);
}

.cancel-button {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.submit-button {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.cancel-button:hover {
  background-color: var(--color-background-soft);
}

.submit-button:hover {
  filter: brightness(1.1);
}

.selected-dates {
  padding: 0.5rem;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  color: var(--color-text);
  font-size: var(--step--1);
}
</style>
