<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from 'vue'
import { useEvents } from '@/composables/useEvents'
import { useMonthColor } from '@/composables/useMonthColor'
import { Maximize2, ArrowLef<PERSON>, ArrowRight } from 'lucide-vue-next'

type Month = {
  index: number
  name: string
}

const props = defineProps({
  allMonths: {
    type: Array as () => Month[],
    required: true
  },
  currentTime: {
    type: Date,
    required: true
  },
  hasOlderEvents: {
    type: Boolean,
    required: true
  }
})

const { events, subscribeToEvents, getEventsDateRange } = useEvents()
const { monthColor, textColor } = useMonthColor()

const currentMonth = computed(() => props.currentTime.getMonth())
const currentYear = computed(() => props.currentTime.getFullYear())

const emit = defineEmits<{
  (e: 'navigate-month', month: number, year: number): void
  (e: 'toggle-size'): void
  (e: 'reset-today'): void
  (e: 'move-backward'): void
  (e: 'move-forward'): void
}>()

const yearOptions = ref<Array<{ value: string, label: string }>>([])

watchEffect(async () => {
  const dateRange = await getEventsDateRange()

  const minYear = dateRange?.years()[0]
  const maxYear = new Date().getFullYear() + 2

  yearOptions.value = Array.from({ length: maxYear - minYear + 1 }, (_, i) => minYear + i).map(year => ({
    value: year.toString(),
    label: year.toString()
  }))
})

const buttonStyles = computed(() => (monthIndex: number) => {
  const isActive = currentMonth.value === monthIndex
  const backgroundColor = monthColor(monthIndex, isActive)
  const activeTextColor = textColor(monthIndex, isActive)
  return {
    backgroundColor,
    color: isActive ? activeTextColor : 'var(--color-text)',
    borderColor: isActive ? backgroundColor : 'transparent'
  }
})

function handleYearChange(value: string | number | null): void {
  if (value === null) return
  const yearValue = typeof value === 'string' ? parseInt(value, 10) : value
  emit('navigate-month', currentMonth.value, yearValue)
}

function handleMonthClick(monthIndex: number): void {
  emit('navigate-month', monthIndex, currentYear.value)
}

onMounted(async () => {
  const fromDate = new Date(currentYear.value, currentMonth.value, 1)
  await subscribeToEvents({ fromDate })
})
</script>

<template>
  <div class="calendar__header">
    <div class="calendar__legend">
      <div class="month-buttons">
        <BaseButton v-for="month in allMonths" :key="month.index" :class="{ active: currentMonth === month.index }"
          :style="buttonStyles(month.index)" @click="handleMonthClick(month.index)" size="compact">
          {{ month.name.slice(0, 3) }}
        </BaseButton>
      </div>
      <BaseSelect class="year-select" :model-value="currentYear.toString()" :options="yearOptions"
        @update:model-value="handleYearChange" :clearable="false" id="calendar-header-year-select" />
    </div>

    <div class="calendar__actions">
      <BaseButton class="nav-button" :disabled="!hasOlderEvents" @click="emit('move-backward')"
        :class="{ 'nav-button--disabled': !hasOlderEvents }">
        <ArrowLeft />
      </BaseButton>
      <BaseButton size="compact" @click="emit('reset-today')">
        Today
      </BaseButton>
      <BaseButton class="nav-button" @click="emit('move-forward')">
        <ArrowRight />
      </BaseButton>
      <BaseButton size="compact" @click="emit('toggle-size')" title="Expand Calendar">
        <Maximize2 :size="20" />
      </BaseButton>
    </div>
  </div>
</template>

<style scoped>
.calendar__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.calendar__legend {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.month-buttons {
  display: flex;
  gap: 0.25rem;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 0.25rem;

  >.active {
    font-weight: 500;
  }
}

.year-select {
  min-width: 6rem;
}

.calendar__actions {
  display: flex;
  gap: 0.5rem;
}

.nav-button {
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.nav-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
