import { Timestamp } from 'firebase/firestore'
import type { Act } from './models'

export interface Song {
  id: string
  title: string
  shortTitle: string
  artist: string
  acts: string[]
  actDetails?: {
    [key: string]: any
  }
  key?: string
  tempo?: number
  duration?: number
  notes?: string
  tags?: string[]
  createdAt?: Timestamp
  updatedAt?: Timestamp
}

export interface Set {
  id: string
  songs: Song[]
  act: Act | null
  name?: string
  notes?: string
  duration?: number
  createdAt?: Timestamp
  updatedAt?: Timestamp
}

export interface SetList {
  id: string
  sets: Set[]
  name: string
  description?: string
  event?: string
  createdAt: Timestamp
  updatedAt: Timestamp
}
