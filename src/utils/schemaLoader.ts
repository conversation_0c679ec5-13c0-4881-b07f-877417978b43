// Define interfaces for schema types
interface SchemaFile {
  default?: Record<string, unknown>
  [key: string]: unknown
}

interface SchemaMap {
  [key: string]: Record<string, unknown>
}

// Use Vite's glob import to get all schema files
const schemaFiles: Record<string, SchemaFile> = import.meta.glob(
  '../schema/*.json',
  { eager: true },
)

// Create a mapping of collection names to their schema files
const schemas: SchemaMap = {}

// Process each schema file
Object.entries(schemaFiles).forEach(([path, schema]) => {
  // Extract the filename without extension from the path
  const match = path.match(/\/schema\/(.+)\.json$/)
  if (match) {
    const name = match[1]
    // Convert singular to plural for collection names if needed
    const collectionName = name.endsWith('Note') ? name + 's' : name + 's'
    schemas[collectionName] =
      schema.default || (schema as Record<string, unknown>)
  }
})

export function loadSchema(
  collectionName: string,
): Record<string, unknown> | null {
  return schemas[collectionName] || null
}
