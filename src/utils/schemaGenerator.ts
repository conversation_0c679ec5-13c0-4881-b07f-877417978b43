import { collection, getDocs } from 'firebase/firestore'
import { useFirebase } from '../composables/useFirebase'

function getFieldType(value: unknown): string {
  if (value === null) return 'null'
  if (value instanceof Date) return 'date-time'
  if (Array.isArray(value)) return 'array'
  if (typeof value === 'object') return 'object'
  return typeof value
}

interface PropertySchema {
  type: string
  format?: string
  items?: PropertySchema | { oneOf: Array<{ type: string }> }
  properties?: Record<string, PropertySchema>
}

function generatePropertySchema(
  value: unknown,
  seenObjects = new Set<object>(),
): PropertySchema {
  if (value === null) {
    return { type: 'null' }
  }

  if (value instanceof Date) {
    return {
      type: 'string',
      format: 'date-time',
    }
  }

  if (Array.isArray(value)) {
    const itemTypes = new Set(value.map(item => getFieldType(item)))
    return {
      type: 'array',
      items:
        itemTypes.size === 1
          ? generatePropertySchema(value[0])
          : { oneOf: Array.from(itemTypes).map(type => ({ type })) },
    }
  }

  if (typeof value === 'object' && value !== null) {
    // Prevent circular references
    if (seenObjects.has(value as object)) {
      return { type: 'object' }
    }
    seenObjects.add(value as object)

    const properties: Record<string, PropertySchema> = {}
    for (const [key, val] of Object.entries(value as object)) {
      properties[key] = generatePropertySchema(val, new Set(seenObjects))
    }

    return {
      type: 'object',
      properties,
    }
  }

  return { type: typeof value }
}

interface CollectionSchema {
  $schema: string
  type: string
  properties: Record<string, PropertySchema>
  required: string[]
}

export async function generateCollectionSchema(
  collectionName: string,
): Promise<CollectionSchema> {
  const { db } = useFirebase()
  const snapshot = await getDocs(collection(db, collectionName))

  const schema: CollectionSchema = {
    $schema: 'http://json-schema.org/draft-07/schema#',
    type: 'object',
    properties: {},
    required: [],
  }

  const fieldOccurrences = new Map<string, number>()
  const totalDocs = snapshot.size

  snapshot.forEach(doc => {
    const data = doc.data()
    Object.entries(data).forEach(([field, value]) => {
      // Track field occurrences
      fieldOccurrences.set(field, (fieldOccurrences.get(field) || 0) + 1)

      // Update schema properties
      if (!schema.properties[field]) {
        schema.properties[field] = generatePropertySchema(value)
      }
    })
  })

  // Fields that appear in all documents are required
  fieldOccurrences.forEach((count, field) => {
    if (count === totalDocs) {
      schema.required.push(field)
    }
  })

  return schema
}

// Usage example:
// const schema = await generateCollectionSchema('acts')
// console.log(JSON.stringify(schema, null, 2))
