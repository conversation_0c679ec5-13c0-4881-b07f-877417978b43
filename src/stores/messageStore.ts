import { defineStore } from 'pinia'
import { ref } from 'vue'

export type DialogType = 'confirm' | 'alert' | 'warning'
export type NotificationType = 'info' | 'success' | 'error' | 'warning'

interface DialogOptions {
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  type?: DialogType
  onConfirm?: () => void
  onCancel?: () => void
}

interface NotificationOptions {
  type?: NotificationType
  message: string
  durationSeconds?: number
}

export const useMessageStore = defineStore('messages', () => {
  const currentDialog = ref<DialogOptions | null>(null)
  const currentNotification = ref<NotificationOptions | null>(null)
  let notificationTimeout: ReturnType<typeof setTimeout> | null = null

  const showDialog = (options: DialogOptions) => {
    currentDialog.value = {
      title: 'Confirmation',
      message: 'Are you sure?',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      type: 'confirm',
      onConfirm: () => {},
      onCancel: () => {},
      ...options,
    }
  }

  const closeDialog = () => {
    currentDialog.value = null
  }

  const showNotification = (options: NotificationOptions) => {
    if (notificationTimeout) {
      clearTimeout(notificationTimeout)
    }

    currentNotification.value = {
      type: 'info',
      durationSeconds: 3,
      ...options,
    }

    const duration = currentNotification.value.durationSeconds ?? 3
    currentNotification.value.durationSeconds = Math.min(
      Math.max(duration, 1),
      10,
    )

    notificationTimeout = setTimeout(() => {
      closeNotification()
    }, currentNotification.value.durationSeconds * 1000)
  }

  const closeNotification = () => {
    currentNotification.value = null
    if (notificationTimeout) {
      clearTimeout(notificationTimeout)
      notificationTimeout = null
    }
  }

  return {
    currentDialog,
    currentNotification,
    showDialog,
    closeDialog,
    showNotification,
    closeNotification,
  }
})
