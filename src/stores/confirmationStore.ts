import { defineStore } from 'pinia'
import { ref } from 'vue'

type ConfirmationCallback = () => Promise<void> | void

export const useConfirmationStore = defineStore('confirmation', () => {
  const isOpen = ref(false)
  const title = ref('')
  const message = ref('')
  const confirmText = ref('Confirm')
  const cancelText = ref('Cancel')
  const onConfirm = ref<ConfirmationCallback | null>(null)
  const onCancel = ref<ConfirmationCallback | null>(null)
  const type = ref<'confirm' | 'alert' | 'warning'>('confirm')

  function showConfirmation(options: {
    title: string
    message: string
    confirmText?: string
    cancelText?: string
    type?: 'confirm' | 'alert' | 'warning'
    onConfirm: ConfirmationCallback
    onCancel?: ConfirmationCallback
  }) {
    title.value = options.title
    message.value = options.message
    confirmText.value = options.confirmText || 'Confirm'
    cancelText.value = options.cancelText || 'Cancel'
    type.value = options.type || 'confirm'
    onConfirm.value = options.onConfirm
    onCancel.value = options.onCancel || null
    isOpen.value = true
  }

  function hideConfirmation() {
    isOpen.value = false
    title.value = ''
    message.value = ''
    onConfirm.value = null
    onCancel.value = null
  }

  async function handleConfirm() {
    if (onConfirm.value) {
      await onConfirm.value()
    }
    hideConfirmation()
  }

  async function handleCancel() {
    if (onCancel.value) {
      await onCancel.value()
    }
    hideConfirmation()
  }

  return {
    isOpen,
    title,
    message,
    confirmText,
    cancelText,
    type,
    onConfirm,
    onCancel,
    showConfirmation,
    hideConfirmation,
    handleConfirm,
    handleCancel,
  }
})
