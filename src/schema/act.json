{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["name", "description", "defaultGigDescription"], "properties": {"id": {"type": "string", "description": "Unique identifier for the act"}, "name": {"type": "string", "description": "Primary name of the act"}, "displayName": {"type": "string", "description": "Optional shorter display name, falls back to name if not provided"}, "description": {"type": "string", "description": "Full description of the act"}, "defaultGigDescription": {"type": "string", "description": "Default description used for gigs"}, "logoUrls": {"type": "object", "properties": {"text": {"type": "string", "format": "uri", "description": "URL for text logo"}, "badge": {"type": "string", "format": "uri", "description": "URL for badge logo"}}}, "photoUrl": {"type": "string", "format": "uri", "description": "URL for act's main photo"}, "website": {"type": "string", "format": "uri", "description": "Act's website URL"}, "artistIds": {"type": "array", "items": {"type": "string"}, "description": "Array of artist IDs associated with this act"}, "artists": {"type": "object", "description": "Map of artist IDs to their instruments", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp of last update"}}}