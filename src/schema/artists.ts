/**
 * @fileoverview Schema definitions for artists collection
 */

/**
 * Artist document schema in Firestore
 * Collection: artists
 *
 * @example
 * {
 *   name: "<PERSON>",
 *   email: "<EMAIL>",
 *   photoURL: "https://example.com/roy.jpg",
 *   bio: "American singer, songwriter...",
 *   website: "https://example.com",
 *   social: {
 *     facebook: "https://facebook.com/roy",
 *     twitter: "https://twitter.com/roy"
 *   },
 *   createdAt: Timestamp,
 *   updatedAt: Timestamp
 * }
 */

export const artistSchema = {
  name: 'string',
  email: 'string',
  photoURL: 'string?',
  bio: 'string?',
  website: 'string?',
  social: {
    facebook: 'string?',
    twitter: 'string?',
    instagram: 'string?',
    youtube: 'string?',
  },
  createdAt: 'timestamp',
  updatedAt: 'timestamp',
}

/**
 * Firestore security rules for artists collection
 * @example
 * match /artists/{artistId} {
 *   allow read: if true;
 *   allow write: if request.auth != null
 *     && (request.auth.token.admin == true
 *     || (exists(/databases/$(database)/documents/users/$(request.auth.uid))
 *     && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.roles[*].type.hasAny(['artist'])));
 * }
 */
