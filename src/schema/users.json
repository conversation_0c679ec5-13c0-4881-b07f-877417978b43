{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["prefs", "username", "tags", "firstName", "email", "lastName"], "properties": {"id": {"type": "string", "description": "Unique identifier for the user (Firebase Auth UID)"}, "email": {"type": "string", "format": "email", "description": "User's email address"}, "firstName": {"type": "string", "description": "User's first name"}, "lastName": {"type": "string", "description": "User's last name"}, "roles": {"type": "array", "items": {"type": "string", "enum": ["admin", "user", "musician"]}, "description": "User's role in the system"}, "isActive": {"type": "boolean", "description": "Whether the user account is active"}, "lastLogin": {"type": "string", "format": "date-time", "description": "Timestamp of user's last login"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the user was created"}, "prefs": {"type": "object", "description": "User preferences", "properties": {"isSubscribed": {"type": "boolean", "description": "Whether the user is subscribed to notifications"}, "viewAsGrid": {"type": "boolean", "description": "Whether the user prefers grid view"}}}, "username": {"type": "string", "description": "User's username"}, "tags": {"type": "array", "description": "User's associated tags", "items": {"type": "string"}}, "phone": {"type": "string", "description": "User's phone number"}, "birthday": {"type": "object", "description": "User's birthday as a Firestore timestamp", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}, "address": {"type": "string", "description": "User's address"}, "isFrightened": {"type": "boolean", "description": "Whether the user is frightened"}}}