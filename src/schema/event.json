{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["title", "when", "duration", "acts"], "properties": {"id": {"type": "string", "description": "Unique identifier for the event"}, "acts": {"type": "array", "items": {"type": "string"}, "description": "Array of act IDs performing at this event"}, "cta": {"type": ["object", "null"], "properties": {"type": {"type": "string", "enum": ["fb", "ticket"]}, "text": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "price": {"type": ["string", "null"]}}, "required": ["type", "text", "url"]}, "title": {"type": "string", "description": "Event title"}, "description": {"type": "string", "description": "Event description"}, "duration": {"type": "number", "description": "Duration in minutes"}, "notes": {"type": "object", "properties": {"fee": {"type": "object", "properties": {"amount": {"type": "number"}, "date": {"type": "string", "format": "date-time"}, "paid": {"type": "boolean"}}}, "deposit": {"type": "object", "properties": {"amount": {"type": "number"}, "date": {"type": "string", "format": "date-time"}, "paid": {"type": "boolean"}}}, "agent": {"type": ["string", "null"]}}}, "status": {"type": "string", "enum": ["draft", "confirmed", "published", "cancelled", "postponed", "rescheduled", "save the date"], "description": "Current status of the event"}, "statusHistory": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "enum": ["draft", "confirmed", "published", "cancelled", "postponed", "rescheduled", "save the date"]}, "date": {"type": "string", "format": "date-time"}, "note": {"type": "string"}}}, "description": "History of status changes"}, "venue": {"type": "string", "description": "Venue ID"}, "when": {"type": "string", "format": "date-time", "description": "Event date and time"}, "isPrivate": {"type": "boolean", "description": "Whether this is a private event"}}}