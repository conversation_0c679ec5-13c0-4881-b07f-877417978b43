{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["artistId", "date", "type"], "properties": {"id": {"type": "string", "description": "Unique identifier for the availability record"}, "artistId": {"type": "string", "description": "Reference to the artist"}, "date": {"type": "string", "format": "date-time", "description": "Date of the availability"}, "type": {"type": "string", "enum": ["unavailable", "tentative", "available"], "description": "Type of availability status"}, "reason": {"type": "string", "description": "Optional reason for unavailability"}, "notes": {"type": "string", "description": "Additional notes about the availability"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the record was created"}}}