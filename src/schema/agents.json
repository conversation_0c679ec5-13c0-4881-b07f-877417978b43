{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["name"], "properties": {"id": {"type": "string", "description": "Unique identifier for the agent"}, "name": {"type": "string", "description": "Agent's full name"}, "email": {"type": "string", "format": "email", "description": "Agent's contact email"}, "phone": {"type": "string", "description": "Agent's contact phone number"}, "commission": {"type": "number", "description": "Default commission percentage", "minimum": 0, "maximum": 100}, "notes": {"type": "string", "description": "Additional notes about the agent"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the agent was created"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp when the agent was last updated"}}}