{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"prefs": {"type": "object", "properties": {"isSubscribed": {"type": "boolean"}, "viewAsGrid": {"type": "boolean"}}}, "username": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "firstName": {"type": "string"}, "email": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}, "birthday": {"type": "object", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}, "address": {"type": "string"}, "isFrightened": {"type": "boolean"}}, "required": ["prefs", "username", "tags", "firstName", "email", "lastName"]}