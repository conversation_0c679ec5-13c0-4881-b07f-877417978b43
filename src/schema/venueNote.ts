interface VenueNote {
  id: string // Same as venue ID (venue-name-town)
  contactMethods: {
    phone: Array<{
      number: string
      type?: 'main' | 'mobile' | 'other'
      primary?: boolean
    }>
    email: Array<{
      address: string
      primary?: boolean
    }>
    social?: Array<{
      platform: 'facebook' | 'instagram' | 'twitter' | 'other'
      url: string
      canMessage?: boolean
    }>
  }
  contacts: Array<{
    name: string
    role: string
    primary?: boolean
    contactMethods?: {
      phone?: string
      email?: string
    }
  }>
  venueDetails: {
    hasLiveMusic?: boolean
    address?: {
      street: string
      city: string
      postcode: string
    }
    features?: string[]
    notes?: string[]
  }
  history: Array<{
    date: Timestamp
    type: 'communication' | 'booking' | 'note'
    content: string
    status?: 'pending' | 'confirmed' | 'cancelled'
    addedBy: string
    metadata?: {
      method?: 'email' | 'phone' | 'in-person'
      outcome?: string
      followUpDate?: Timestamp
    }
  }>
  metadata: {
    createdAt: Timestamp
    updatedAt: Timestamp
    lastContactDate: Timestamp
    source: string
  }
}
