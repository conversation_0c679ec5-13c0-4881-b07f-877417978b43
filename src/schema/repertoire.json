{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["id", "karaokeRef", "shortTitle", "tempo", "artist", "bvs", "title", "released", "videoRefUrl", "percussion", "acts", "key", "multitrack", "timeSignature"], "properties": {"id": {"type": "string", "description": "Unique identifier for the song", "example": "walk-on"}, "karaokeRef": {"type": "string", "description": "Reference number for karaoke version", "example": "PS14530"}, "shortTitle": {"type": "string", "description": "Abbreviated or short version of the song title", "example": "Walk On"}, "tempo": {"type": "number", "description": "Song tempo in beats per minute", "example": 196}, "artist": {"type": "string", "description": "Original artist of the song", "example": "<PERSON>"}, "bvs": {"type": "boolean", "description": "Whether the song has backing vocals", "example": true}, "title": {"type": "string", "description": "Full title of the song", "example": "Walk On"}, "released": {"type": "number", "description": "Year the song was released", "example": 1969}, "videoRefUrl": {"type": "string", "format": "uri", "description": "Reference video URL", "example": "https://www.youtube.com/watch?v=JGlD6Ho24lU"}, "percussion": {"type": "boolean", "description": "Whether the song has percussion", "example": false}, "acts": {"type": "array", "items": {"type": "string"}, "description": "List of acts that perform this song", "example": ["daves-roy-orbison", "human-jukebox", "orbison-and-human-jukebox", "orbison-project"]}, "key": {"type": "string", "description": "Musical key of the song", "example": "C/D"}, "multitrack": {"type": "boolean", "description": "Whether the song has multitrack recording", "example": true}, "timeSignature": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2, "description": "Time signature of the song [beats per measure, beat unit]", "example": [4, 4]}}, "additionalProperties": false}