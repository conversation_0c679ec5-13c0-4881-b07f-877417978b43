{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["name", "type"], "properties": {"id": {"type": "string", "description": "Unique identifier for the contact"}, "type": {"type": "string", "enum": ["venue", "agent", "band", "solo", "organization"], "description": "Type of contact"}, "name": {"type": "string", "description": "Contact name"}, "organization": {"type": "string", "description": "Organization or company name"}, "role": {"type": "string", "description": "Role or position"}, "contactMethods": {"type": "object", "properties": {"email": {"type": "array", "items": {"type": "object", "properties": {"address": {"type": "string", "format": "email"}, "label": {"type": "string"}, "isPrimary": {"type": "boolean"}}}}, "phone": {"type": "array", "items": {"type": "object", "properties": {"number": {"type": "string"}, "label": {"type": "string"}, "isPrimary": {"type": "boolean"}}}}}}, "address": {"type": "object", "properties": {"street": {"type": "string"}, "city": {"type": "string"}, "county": {"type": "string"}, "postcode": {"type": "string"}}}, "notes": {"type": "object", "description": "Key-value pairs for various notes", "additionalProperties": {"type": "string"}}, "history": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "type": {"type": "string", "enum": ["note", "interaction", "booking", "inquiry"]}, "content": {"type": "string"}}}}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Custom tags for categorization"}, "status": {"type": "string", "enum": ["active", "inactive", "potential", "archived"], "default": "active"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}