{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["content", "author"], "properties": {"id": {"type": "string", "description": "Unique identifier for the testimonial"}, "content": {"type": "string", "description": "Testimonial content"}, "author": {"type": "string", "description": "Name of person giving testimonial"}, "actId": {"type": "string", "description": "Reference to the act this testimonial is for"}, "date": {"type": "string", "format": "date-time", "description": "Date the testimonial was given"}, "rating": {"type": "number", "minimum": 1, "maximum": 5, "description": "Optional rating out of 5"}, "isPublished": {"type": "boolean", "description": "Whether the testimonial is publicly visible"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the testimonial was created"}}}