{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["name", "address"], "properties": {"id": {"type": "string", "description": "Unique identifier for the venue"}, "name": {"type": "string", "description": "Venue name"}, "address": {"type": "object", "required": ["town", "postcode"], "properties": {"address1": {"type": "string", "description": "First line of address"}, "address2": {"type": "string", "description": "Second line of address"}, "town": {"type": "string", "description": "Town/City"}, "county": {"type": "string", "description": "County"}, "postcode": {"type": "string", "description": "Postal code"}}}, "website": {"type": "string", "format": "uri", "description": "Venue website URL"}, "phone": {"type": "string", "description": "Contact phone number"}, "email": {"type": "string", "format": "email", "description": "Contact email address"}, "description": {"type": "string", "description": "Optional venue description"}}}