{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["to", "subject", "message"], "properties": {"id": {"type": "string", "description": "Unique identifier for the email"}, "to": {"type": "string", "format": "email", "description": "Recipient email address"}, "from": {"type": "string", "format": "email", "description": "Sender email address"}, "subject": {"type": "string", "description": "Email subject line"}, "message": {"type": "string", "description": "Email message content"}, "sent": {"type": "boolean", "description": "Whether the email has been sent"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the email was created"}, "sentAt": {"type": "string", "format": "date-time", "description": "Timestamp when the email was sent"}}}