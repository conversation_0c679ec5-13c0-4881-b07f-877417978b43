/**
 * @fileoverview Schema definitions for users collection
 */

/**
 * User document schema in Firestore
 * Collection: users
 *
 * @example
 * {
 *   email: "<EMAIL>",
 *   firstName: "<PERSON>",
 *   lastName: "<PERSON><PERSON>",
 *   username: "joh<PERSON><PERSON>",
 *   photoURL: "https://example.com/photo.jpg",
 *   phone: "+1234567890",
 *   address: "123 Main St",
 *   birthday: Timestamp,
 *   isFrightened: false,
 *   roles: [
 *     { type: "artist", artistId: "abc123" },
 *     { type: "bandLeader", actId: "xyz789" },
 *     { type: "admin" }
 *   ],
 *   prefs: {
 *     isSubscribed: true,
 *     viewAsGrid: false
 *   },
 *   tags: ["musician", "vocalist"],
 *   createdAt: Timestamp,
 *   updatedAt: Timestamp
 * }
 */

export const userSchema = {
  email: 'string',
  firstName: 'string',
  lastName: 'string',
  username: 'string?',
  photoURL: 'string?',
  phone: 'string?',
  address: 'string?',
  birthday: 'timestamp?',
  roles: [{
    type: 'string',      // e.g., 'artist', 'bandLeader', 'admin'
    artistId: 'string?', // Conditionally required for 'artist' role
    actId: 'string?'     // Conditionally required for 'bandLeader' role
  }],
  prefs: {
    isSubscribed: 'boolean',
    viewAsGrid: 'boolean'
  },
  tags: ['string'],
  createdAt: 'timestamp',
  updatedAt: 'timestamp'
}

/**
 * Firestore security rules for users collection
 * @example
 * match /users/{userId} {
 *   allow read: if request.auth != null;
 *   allow write: if request.auth.uid == userId;
 *   allow update: if request.auth.uid == userId
 *     || (request.auth.token.admin == true
 *     && request.resource.data.diff(resource.data).affectedKeys()
 *       .hasOnly(['roles', 'tags', 'updatedAt']));
 * }
 */
