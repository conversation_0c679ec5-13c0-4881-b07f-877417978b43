interface Venue {
  id: string
  name: string
  type: 'social_club' | 'pub' | 'legion' | 'theatre' | 'hotel' | 'community'
  subtype?: string
  address: {
    street: string
    city: string
    county?: string
    postcode: string
    country: string
  }
  contactMethods: {
    phone: Array<{
      number: string
      type?: 'main' | 'mobile' | 'other'
      primary?: boolean
    }>
    email: Array<{
      address: string
      primary?: boolean
    }>
  }
  contacts: Array<{
    id: string
    name: string
    role: string
    primary?: boolean
    contactMethods?: {
      phone?: string
      email?: string
    }
  }>
  history: Array<{
    date: Timestamp
    type: 'communication' | 'booking' | 'note'
    content: string
    status?: 'pending' | 'confirmed' | 'cancelled'
    addedBy: string
    metadata?: {
      method?: 'email' | 'phone' | 'in-person'
      outcome?: string
      followUpDate?: Timestamp
    }
  }>
  metadata: {
    createdAt: Timestamp
    updatedAt: Timestamp
    lastContactDate: Timestamp
    source: string
  }
}
