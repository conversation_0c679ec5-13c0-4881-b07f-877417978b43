{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["filename", "processedAt", "status"], "properties": {"id": {"type": "string", "description": "Unique identifier for the processing record"}, "filename": {"type": "string", "description": "Original filename"}, "fileHash": {"type": "string", "description": "Hash of file contents to detect changes"}, "processedAt": {"type": "string", "format": "date-time", "description": "When the file was processed"}, "status": {"type": "string", "enum": ["success", "partial", "failed"], "description": "Processing status"}, "contactsFound": {"type": "number", "description": "Number of contacts extracted"}, "contactIds": {"type": "array", "items": {"type": "string"}, "description": "IDs of contacts created from this file"}, "processingNotes": {"type": "string", "description": "Any notes about the processing"}, "errorDetails": {"type": "string", "description": "Details of any errors encountered"}}}