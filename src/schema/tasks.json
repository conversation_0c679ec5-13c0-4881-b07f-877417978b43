{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"isUrgent": {"type": "boolean", "description": "Indicates if the task requires immediate attention"}, "isCompleted": {"type": "boolean", "description": "Indicates if the task has been completed"}, "details": {"type": "string", "description": "Optional detailed description of the task"}, "dueDate": {"type": "object", "description": "Optional Firestore timestamp indicating when the task is due", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}, "assignedTo": {"type": ["string", "null"], "description": "Email of the user assigned to the task, or null if unassigned"}, "createdAt": {"type": "object", "description": "Firestore timestamp indicating when the task was created", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}, "author": {"type": "string", "description": "Name of the person who created the task"}, "title": {"type": "string", "description": "Brief title of the task"}, "completedBy": {"type": ["string", "null"], "description": "Name of the person who completed the task"}, "completedAt": {"type": "object", "description": "Firestore timestamp indicating when the task was completed", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}, "authorEmail": {"type": "string", "description": "Email address of the task author"}, "updatedAt": {"type": "object", "description": "Firestore timestamp indicating when the task was last updated", "properties": {"seconds": {"type": "number"}, "nanoseconds": {"type": "number"}}}}, "required": ["is<PERSON><PERSON>", "isCompleted", "createdAt", "author", "title"]}