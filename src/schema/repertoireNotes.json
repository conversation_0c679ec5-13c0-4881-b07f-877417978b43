{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["repertoireId"], "properties": {"id": {"type": "string", "description": "Unique identifier for the repertoire note"}, "repertoireId": {"type": "string", "description": "Reference to the repertoire item"}, "performanceNotes": {"type": "string", "description": "Notes about performing the song"}, "technicalNotes": {"type": "string", "description": "Technical requirements or setup notes"}, "arrangements": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "Musical key of this arrangement"}, "notes": {"type": "string", "description": "Notes about this arrangement"}}}}, "lastPerformed": {"type": "string", "format": "date-time", "description": "Date the song was last performed"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the note was created"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp when the note was last updated"}}}