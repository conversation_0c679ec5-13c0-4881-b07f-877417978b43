{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the venue notes"}, "agent": {"type": "string", "description": "Agent ID reference"}, "commission": {"type": "number", "description": "Commission percentage"}, "defaultDeposit": {"type": "number", "description": "Default deposit amount"}, "defaultFee": {"type": "number", "description": "Default fee amount"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}}