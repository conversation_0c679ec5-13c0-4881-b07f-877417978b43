{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["eventId"], "properties": {"id": {"type": "string", "description": "Unique identifier for the event note"}, "eventId": {"type": "string", "description": "Reference to the event"}, "fee": {"type": "object", "properties": {"amount": {"type": "number", "description": "Fee amount"}, "date": {"type": "string", "format": "date-time", "description": "Due date for the fee"}, "paid": {"type": "boolean", "description": "Whether the fee has been paid"}}}, "deposit": {"type": "object", "properties": {"amount": {"type": "number", "description": "Deposit amount"}, "date": {"type": "string", "format": "date-time", "description": "Due date for the deposit"}, "paid": {"type": "boolean", "description": "Whether the deposit has been paid"}}}, "agent": {"type": "object", "properties": {"agentId": {"type": "string", "description": "Reference to the agent"}, "percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "Agent's commission percentage"}}}, "internalNotes": {"type": "string", "description": "Private notes for internal use"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the note was created"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp when the note was last updated"}}}