import {
  Timestamp,
  DocumentSnapshot,
  QuerySnapshot,
  FirestoreError,
} from 'firebase/firestore'
import { AuthErrorCodes } from 'firebase/auth'
import type { AuthError } from 'firebase/auth'

// Convert Firebase Timestamp to Date
export const timestampToDate = (
  timestamp: Timestamp | Date | null | undefined,
): Date | null => {
  if (timestamp instanceof Date) return timestamp
  if (timestamp instanceof Timestamp) return timestamp.toDate()
  return null
}

// Convert Date to Firebase Timestamp
export const dateToTimestamp = (
  date: Timestamp | Date | null | undefined,
): Timestamp | null => {
  if (date instanceof Timestamp) return date
  if (date instanceof Date) return Timestamp.fromDate(date)
  return null
}

export const getDateFrom = (
  timestamp: Timestamp | Date | null | undefined,
): Date => {
  return timestampToDate(timestamp) ?? (new Date() as Date)
}

export const getTimestampFrom = (
  date: Timestamp | Date | null | undefined,
): Timestamp => {
  return dateToTimestamp(date) ?? Timestamp.now()
}

// Format error messages
export const formatFirebaseError = (
  error: FirestoreError | AuthError,
): string => {
  const errorCode = error.code
  switch (errorCode) {
    case AuthErrorCodes.USER_DELETED:
    case AuthErrorCodes.INVALID_PASSWORD:
      return 'Invalid email or password'
    case AuthErrorCodes.TOO_MANY_ATTEMPTS_TRY_LATER:
      return 'Too many failed login attempts. Please try again later'
    case 'permission-denied':
      return 'You do not have permission to perform this action'
    case 'not-found':
      return 'The requested resource was not found'
    default:
      return error.message
  }
}

// Convert Firestore document to data object
export const docToData = <T>(doc: DocumentSnapshot): T | null => {
  const data = doc.data()
  return data ? ({ id: doc.id, ...data } as T) : null
}

// Convert Firestore snapshot to data array
export const snapshotToData = <T>(snapshot: QuerySnapshot): T[] => {
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T)
}

// Handle nested field updates
export const handleNestedFieldUpdate = (
  field: string,
  value: any,
): Record<string, any> => {
  const fields = field.split('.')
  const result: Record<string, any> = {}
  let current = result

  for (let i = 0; i < fields.length - 1; i++) {
    current[fields[i]] = {}
    current = current[fields[i]]
  }

  current[fields[fields.length - 1]] = value
  return result
}
