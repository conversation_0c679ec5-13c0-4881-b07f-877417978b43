// src/models/Act.ts
import { Timestamp } from 'firebase/firestore'
import type { Unsubscribe } from 'firebase/firestore'

export type LogoUrls = {
  text?: string | null
  badge?: string | null
}

export class Act {
  id!: string
  name!: string
  description?: string
  defaultGigDescription?: string
  displayName?: string | null
  website?: string | null
  logoUrls: LogoUrls = {}
  photoUrl?: string | null
  artistIds: string[] = []
  artists: Record<string, string[]> = {}
  createdAt!: Timestamp
  updatedAt: Timestamp | null = null
  private _detailsLoaded: boolean = false
  unsubscribes: Set<Unsubscribe> = new Set()

  constructor(data: Act & { id: string }) {
    if (!data) throw new Error('Act data is required')
    if (!data.id) throw new Error('Act ID is required')

    // Normalize the input data with defaults
    const normalizedData = {
      id: data.id,
      name: data.name,
      description: data.description || '',
      defaultGigDescription: data.defaultGigDescription || '',
      displayName: data.displayName || null,
      website: data.website || null,
      logoUrls: data.logoUrls || {},
      photoUrl: data.photoUrl || null,
      artistIds: data.artistIds || [],
      artists: data.artists || {},
      createdAt: data.createdAt,
      updatedAt: data.updatedAt || null,
    }

    // Assign normalized data to properties
    Object.assign(this, normalizedData)
  }

  // Display methods
  getDisplayName(): string {
    return this.displayName || this.name
  }

  getDescription(): string {
    return this.description || ''
  }

  getDefaultGigDescription(): string {
    return this.defaultGigDescription || ''
  }

  getWebsite(): string {
    return this.website || ''
  }

  getLogoUrl(type: keyof LogoUrls = 'text'): string | null {
    return this.logoUrls?.[type] || null
  }

  getPhotoUrl(): string | null {
    return this.photoUrl || null
  }

  // Artist-related methods
  getArtistIds(): string[] {
    return this.artistIds
  }

  hasArtist(artistId: string): boolean {
    return this.artistIds.includes(artistId)
  }

  getArtistInstruments(artistId: string): string[] {
    return this.artists[artistId] || []
  }

  // Save methods
  private saveCallback?: (id: string, updates: Partial<Act>) => Promise<void>

  setSaveCallback(
    callback: (id: string, updates: Partial<Act>) => Promise<void>,
  ) {
    this.saveCallback = callback
  }

  private async save(updates?: Partial<Act>) {
    if (!this.saveCallback) {
      throw new Error('No save callback provided to Act instance')
    }

    const updateData = updates || {
      name: this.name,
      description: this.description,
      defaultGigDescription: this.defaultGigDescription,
      displayName: this.displayName,
      website: this.website,
      logoUrls: this.logoUrls,
      photoUrl: this.photoUrl,
      artistIds: this.artistIds,
      artists: this.artists,
      updatedAt: Timestamp.now(),
    }

    await this.saveCallback(this.id, updateData)
  }

  // Cleanup method
  cleanup() {
    this.unsubscribes.forEach(unsubscribe => unsubscribe())
    this.unsubscribes.clear()
  }

  // Loading state
  get isFullyLoaded(): boolean {
    return this._detailsLoaded
  }

  // Static methods
  static fromFirestore(doc: { id: string; data: () => any }): Act {
    const data = doc.data()
    return new Act({
      id: doc.id,
      ...data,
    })
  }
}
