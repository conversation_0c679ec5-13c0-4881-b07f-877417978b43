import { doc, updateDoc, Timestamp } from 'firebase/firestore'
import { db } from '../firebase/config'
import { useDateTime } from '../composables/useDateTime'

const { getEndTime } = useDateTime()

export interface UnavailabilityData {
  id?: string
  artistId: string
  startDate: Date | Timestamp
  endDate: Date | Timestamp
  reason?: string
  status?: 'active' | 'cancelled'
  createdAt?: Timestamp
  updatedAt?: Timestamp
}

export class Unavailability {
  id: string
  artistId: string
  startDate: Date
  endDate: Date
  reason: string
  status: 'active' | 'cancelled'
  createdAt: Timestamp
  updatedAt: Timestamp

  constructor(data: UnavailabilityData) {
    this.id = data.id || ''
    this.artistId = data.artistId
    this.startDate =
      data.startDate instanceof Timestamp
        ? data.startDate.toDate()
        : new Date(data.startDate)
    this.endDate =
      data.endDate instanceof Timestamp
        ? data.endDate.toDate()
        : new Date(data.endDate)
    this.reason = data.reason || ''
    this.status = data.status || 'active'
    this.createdAt = data.createdAt || Timestamp.now()
    this.updatedAt = data.updatedAt || Timestamp.now()

    console.log('Created Unavailability:', {
      id: this.id,
      artistId: this.artistId,
      startDate: this.startDate,
      endDate: this.endDate,
      status: this.status,
    })
  }

  // Update methods
  async update(data: Partial<UnavailabilityData>): Promise<void> {
    const docRef = doc(db, 'artistAvailability', this.id)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now(),
    })
  }

  // Status methods
  async cancel(): Promise<void> {
    await this.update({ status: 'cancelled' })
    this.status = 'cancelled'
  }

  async activate(): Promise<void> {
    await this.update({ status: 'active' })
    this.status = 'active'
  }

  // Helper methods
  isActive(): boolean {
    return this.status === 'active'
  }

  isCancelled(): boolean {
    return this.status === 'cancelled'
  }

  overlaps(date: Date): boolean {
    if (!this.startDate || !this.endDate) return false
    return date >= this.startDate && date <= this.endDate
  }

  toFirestore(): UnavailabilityData {
    return {
      id: this.id,
      artistId: this.artistId,
      startDate: this.startDate,
      endDate: this.endDate,
      reason: this.reason,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }

  static fromFirestore(id: string, data: UnavailabilityData): Unavailability {
    return new Unavailability({ id, ...data })
  }
}
