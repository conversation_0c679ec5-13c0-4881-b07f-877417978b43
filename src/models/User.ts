import { Timestamp } from 'firebase/firestore'
import type { UserRole } from '@/types/user'

export interface UserPreferences {
  isSubscribed: boolean
  viewAsGrid: boolean
}

export interface UserData {
  id?: string
  email: string
  firstName: string
  lastName: string
  username: string
  prefs?: UserPreferences
  tags?: string[]
  phone?: string
  birthday?: Timestamp | null
  address?: string
  roles?: UserRole[]
  photoURL?: string | null
  isDisabled?: boolean
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

/**
 * Class representing a User in the system
 */
export class User {
  id: string
  email: string
  firstName: string
  lastName: string
  username: string
  prefs: UserPreferences
  tags: string[]
  phone: string
  birthday: Timestamp | null
  address: string
  roles: UserRole[]
  photoURL: string | null
  isDisabled: boolean
  createdAt: Timestamp | null
  updatedAt: Timestamp | null

  constructor(data: UserData) {
    this.id = data.id || ''
    this.email = data.email
    this.firstName = data.firstName
    this.lastName = data.lastName
    this.username = data.username
    this.prefs = {
      isSubscribed: data.prefs?.isSubscribed ?? false,
      viewAsGrid: data.prefs?.viewAsGrid ?? false,
    }
    this.tags = data.tags || []
    this.phone = data.phone || ''
    this.birthday = data.birthday || null
    this.address = data.address || ''
    this.roles = data.roles || []
    this.photoURL = data.photoURL || null
    this.isDisabled = data.isDisabled || false
    // Default to current timestamp for new users, preserve existing timestamp for loaded users
    this.createdAt = data.createdAt || Timestamp.now()
    this.updatedAt = data.updatedAt || null
  }

  get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim()
  }

  get displayName(): string {
    return this.username || this.fullName
  }

  get roleNames(): string {
    if (!this.roles?.length) return ''

    return this.roles
      .map(role => {
        if (!role.type) return ''

        switch (role.type.toLowerCase()) {
          case 'artist':
            return 'Artist'
          case 'bandleader':
            return 'Band Leader'
          case 'admin':
            return 'Administrator'
          default:
            return role.type.charAt(0).toUpperCase() + role.type.slice(1)
        }
      })
      .filter(Boolean)
      .join(', ')
  }

  hasRole(roleName: string): boolean {
    return this.roles.some(
      role => role.type?.toLowerCase() === roleName.toLowerCase(),
    )
  }

  getRoleData(roleName: string): UserRole | null {
    const role = this.roles.find(
      r => r.type?.toLowerCase() === roleName.toLowerCase(),
    )
    return role || null
  }

  get avatarURL(): string | null {
    return this.photoURL || null
  }

  get isSubscribed(): boolean {
    return this.prefs?.isSubscribed || false
  }

  toFirestore(): Omit<UserData, 'id'> {
    return {
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      username: this.username,
      prefs: this.prefs,
      tags: this.tags,
      phone: this.phone,
      birthday: this.birthday,
      address: this.address,
      roles: this.roles,
      photoURL: this.photoURL,
      isDisabled: this.isDisabled,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }

  static fromFirestore(data: UserData): User {
    return new User(data)
  }
}
