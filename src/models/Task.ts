import { Timestamp } from 'firebase/firestore'

interface TaskData {
  id?: string
  title: string // required
  details?: string // optional
  isUrgent: boolean // required
  isCompleted: boolean // required
  author: string // required
  authorEmail?: string
  assignedTo?: string | null
  dueDate?: Timestamp | null
  completedBy?: string | null
  createdAt: Timestamp | null // required but can be null initially
  updatedAt: Timestamp | null // can be null initially
  completedAt?: Timestamp | null
}

interface UpdateTaskData {
  title?: string
  details?: string
  isUrgent?: boolean
  isCompleted?: boolean
  assignedTo?: string | null
  dueDate?: Date | undefined
}

/**
 * Class representing a Task in the system
 */
export class Task {
  id: string
  title: string
  private _details?: string
  isUrgent: boolean
  isCompleted: boolean
  author: string
  authorEmail?: string
  assignedTo: string | null
  private _dueDate?: Date
  completedBy: string | null
  createdAt: Date
  updatedAt: Date
  completedAt?: Date | null

  constructor(data: TaskData) {
    this.id = data.id || ''
    this.title = data.title
    this._details = data.details
    this.isUrgent = data.isUrgent
    this.isCompleted = data.isCompleted
    this.author = data.author
    this.authorEmail = data.authorEmail
    this.assignedTo = data.assignedTo || null
    this._dueDate =
      data.dueDate && 'seconds' in data.dueDate
        ? new Date(data.dueDate.seconds * 1000)
        : undefined
    this.completedBy = data.completedBy || null

    // Handle timestamps - if they don't exist or are null, set to current date
    this.createdAt =
      data.createdAt && data.createdAt !== null && 'seconds' in data.createdAt
        ? new Date(data.createdAt.seconds * 1000)
        : new Date()
    this.updatedAt =
      data.updatedAt && data.updatedAt !== null && 'seconds' in data.updatedAt
        ? new Date(data.updatedAt.seconds * 1000)
        : new Date()
    this.completedAt =
      data.completedAt &&
      data.completedAt !== null &&
      'seconds' in data.completedAt
        ? new Date(data.completedAt.seconds * 1000)
        : null
  }

  get details(): string | undefined {
    return this._details
  }

  set details(value: string | undefined) {
    this._details = value
  }

  get dueDate(): Date | undefined {
    return this._dueDate
  }

  set dueDate(value: Date | undefined) {
    this._dueDate = value
  }

  get dueDateFormatted(): string {
    if (!this._dueDate) return ''
    return this._dueDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
  }

  get isOverdue(): boolean {
    if (!this._dueDate || this.isCompleted) return false
    return this._dueDate < new Date()
  }

  get isDueSoon(): boolean {
    if (!this._dueDate || this.isCompleted) return false
    const now = new Date()
    const threeDaysFromNow = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000)
    return this._dueDate > now && this._dueDate <= threeDaysFromNow
  }

  get creationInfo(): string {
    const date = this.createdAt.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
    return `Created by ${this.author} on ${date}`
  }

  get completionInfo(): string | null {
    if (!this.isCompleted || !this.completedAt || !this.completedBy) return null
    const date = this.completedAt.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
    return `Completed by ${this.completedBy} on ${date}`
  }

  toFirestore(): Omit<TaskData, 'id'> {
    return {
      title: this.title,
      details: this._details,
      isUrgent: this.isUrgent,
      isCompleted: this.isCompleted,
      author: this.author,
      authorEmail: this.authorEmail,
      assignedTo: this.assignedTo,
      dueDate: this._dueDate ? Timestamp.fromDate(this._dueDate) : undefined,
      completedBy: this.completedBy,
      createdAt: Timestamp.fromDate(this.createdAt),
      updatedAt: Timestamp.fromDate(this.updatedAt),
      completedAt: this.completedAt
        ? Timestamp.fromDate(this.completedAt)
        : null,
    }
  }

  static fromFirestore(id: string, data: TaskData): Task {
    return new Task({ id, ...data })
  }

  /**
   * Updates the task with new data
   * @param data The data to update the task with
   */
  update(data: UpdateTaskData): UpdateTaskData {
    // Update local properties
    if (data.title !== undefined) this.title = data.title
    if (data.details !== undefined) this._details = data.details
    if (data.isUrgent !== undefined) this.isUrgent = data.isUrgent
    if (data.isCompleted !== undefined) this.isCompleted = data.isCompleted
    if (data.assignedTo !== undefined) this.assignedTo = data.assignedTo
    if (data.dueDate !== undefined) this._dueDate = data.dueDate

    // Return the data in the format expected by Firestore
    return {
      title: data.title,
      details: data.details,
      isUrgent: data.isUrgent,
      isCompleted: data.isCompleted,
      assignedTo: data.assignedTo,
      dueDate: data.dueDate,
    }
  }
}
