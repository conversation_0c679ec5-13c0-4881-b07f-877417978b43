import { doc, updateDoc, Timestamp } from 'firebase/firestore'
import { db } from '../firebase/config'

export type AddressData = {
  address1?: string
  address2?: string
  town?: string
  county?: string
  postcode?: string
  coords?: string
}

export type venueNotes = {
  fee?: number
  deposit?: number
  agentId?: string
  commission?: number
  percentage?: number
  comments?: string[]
}

export type VenueData = {
  id?: string
  name?: string
  address?: AddressData
  phone?: string
  email?: string
  website?: string
  notes?: venueNotes
  status?: string
}

export interface VenueContactInfo {
  phone: string
  email: string
  website: string
}

export class Address {
  address1: string
  address2: string
  town: string
  county: string
  postcode: string
  coords?: string

  constructor(data: AddressData = {}) {
    this.address1 = data.address1 || ''
    this.address2 = data.address2 || ''
    this.town = data.town || ''
    this.county = data.county || ''
    this.postcode = data.postcode || ''
    this.coords = data.coords || ''
  }

  toString(): string {
    return [this.address1, this.address2, this.town, this.county, this.postcode]
      .filter(Boolean)
      .join(', ')
  }

  toFirestore(): AddressData {
    const data: AddressData = {}

    if (this.address1) data.address1 = this.address1
    if (this.address2) data.address2 = this.address2
    if (this.town) data.town = this.town
    if (this.county) data.county = this.county
    if (this.postcode) data.postcode = this.postcode
    if (this.coords) data.coords = this.coords

    return data
  }

  static fromPlainObject(data: AddressData): Address {
    return new Address(data)
  }
}

export class Venue {
  id: string
  name: string
  address: Address
  phone: string
  email: string
  website: string
  notes: venueNotes
  status: string

  constructor(data: VenueData = {}) {
    this.id = data.id || ''
    this.name = data.name || ''
    this.address = data.address ? new Address(data.address) : new Address()
    this.phone = data.phone || ''
    this.email = data.email || ''
    this.website = data.website || ''
    this.notes = data.notes || {}
    this.status = data.status || 'active'
  }

  getName(): string {
    return this.name || 'Unnamed Venue'
  }

  getFormattedAddress(): string {
    return this.address.toString()
  }

  getCoords(): string {
    return this.address.coords || ''
  }

  getContactInfo(): VenueContactInfo {
    return {
      phone: this.phone,
      email: this.email,
      website: this.website,
    }
  }

  isActive(): boolean {
    return this.status === 'active'
  }

  async setStatus(status: string): Promise<void> {
    const docRef = doc(db, 'venues', this.id)
    await updateDoc(docRef, { status })
    this.status = status
  }

  toFirestore(): VenueData {
    const data: VenueData = {}

    if (this.name !== undefined) data.name = this.name
    if (this.address !== undefined) data.address = this.address.toFirestore()
    if (this.phone !== undefined) data.phone = this.phone
    if (this.email !== undefined) data.email = this.email
    if (this.website !== undefined) data.website = this.website
    if (this.notes !== undefined) data.notes = this.notes
    if (this.status !== undefined) data.status = this.status

    return data
  }

  async update(data: Partial<VenueData>): Promise<void> {
    const docRef = doc(db, 'venues', this.id)
    const updateData: Partial<VenueData> = {}

    if (data.name !== undefined) {
      this.name = data.name
      updateData.name = data.name
    }
    if (data.address !== undefined) {
      this.address = new Address(data.address)
      updateData.address = this.address.toFirestore()
    }
    if (data.phone !== undefined) {
      this.phone = data.phone
      updateData.phone = data.phone
    }
    if (data.email !== undefined) {
      this.email = data.email
      updateData.email = data.email
    }
    if (data.website !== undefined) {
      this.website = data.website
      updateData.website = data.website
    }
    if (data.notes !== undefined) {
      this.notes = data.notes
      updateData.notes = data.notes
    }
    if (data.status !== undefined) {
      this.status = data.status
      updateData.status = data.status
    }

    await updateDoc(docRef, updateData)
  }

  static fromFirestore(doc: { id: string; data(): VenueData }): Venue {
    const data = doc.data()
    return new Venue({
      id: doc.id,
      ...data,
    })
  }

  static fromPlainObject(data: VenueData): Venue {
    return new Venue(data)
  }
}
