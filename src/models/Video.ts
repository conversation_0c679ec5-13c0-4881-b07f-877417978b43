// @ts-nocheck
import { Timestamp } from 'firebase/firestore'

export class Video {
  id: string
  title: string
  description: string
  createdAt: Timestamp
  date: Timestamp

  constructor({ id, title, description, createdAt, date }) {
    this.id = id
    this.title = title
    this.description = description
    this.createdAt = createdAt
    this.date = date
  }

  get date() {
    return this.date.toDate().toLocaleDateString()
  }
}
