import type { App, Plugin } from 'vue'
import BaseBadge from '@/components/base/BaseBadge.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import BaseCard from '@/components/base/BaseCard.vue'
import BaseDialog from '@/components/base/BaseDialog.vue'
import BaseDropdown from '@/components/base/BaseDropdown.vue'
import BaseEditor from '@/components/base/BaseEditor.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import BasePhotoUpload from '@/components/base/BasePhotoUpload.vue'
import BaseSelect from '@/components/base/BaseSelect.vue'
import BaseSection from '@/components/base/BaseSection.vue'
import BaseToggle from '@/components/base/BaseToggle.vue'
import CloudImage from '@/components/base/CloudImage.vue'
import LoadingSpinner from '@/components/base/LoadingSpinner.vue'

export const baseComponents: Plugin = {
  install(app: App): void {
    // Register base components globally
    app.component('BaseBadge', BaseBadge)
    app.component('BaseButton', BaseButton)
    app.component('BaseCard', BaseCard)
    app.component('BaseDialog', BaseDialog)
    app.component('BaseDropdown', BaseDropdown)
    app.component('BaseEditor', BaseEditor)
    app.component('BaseInput', BaseInput)
    app.component('BasePhotoUpload', BasePhotoUpload)
    app.component('BaseSelect', BaseSelect)
    app.component('BaseSection', BaseSection)
    app.component('BaseToggle', BaseToggle)
    app.component('CloudImage', CloudImage)
    app.component('LoadingSpinner', LoadingSpinner)
  },
}
