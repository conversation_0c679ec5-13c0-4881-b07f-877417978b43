import Toast, { type PluginOptions, POSITION } from 'vue-toastification'
import type { ToastOptions } from 'vue-toastification/dist/types/types'
import 'vue-toastification/dist/index.css'

export const toastOptions: ToastOptions & PluginOptions = {
  position: POSITION.TOP_RIGHT,
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false,
}

export default Toast
